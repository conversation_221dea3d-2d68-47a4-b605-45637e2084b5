<route lang="json5">
{
  style: {
    navigationBarTitleText: '换课',
  },
}
</route>
<template>
  <view class="container">
    <ky-scroll-view
      :apiFun="
        (params) =>
          httpGet('/study/portal/course/changeCourse/page', {
            ...params,
            srcCourseId: route.query.id,
          })
      "
    >
      <template #default="{ data }">
        <view class="list p-20rpx">
          <view v-for="item in data" :key="item.id" class="item" @click="click_course(item)">
            <ky-image
              :src="item.cimage"
              width="100%"
              height="194rpx"
              mode="widthFix"
              rounded="10rpx 10rpx 0 0"
            />
            <view class="content">
              <view class="ellipsis text-26rpx font-bold">{{ item.cname }}</view>
              <view class="mt-18rpx mb-21rpx text-20rpx text-#B6B6B6 ellipsis">
                适用于 {{ item.reportTime }} 等年
              </view>
              <view class="flex items-center gap-11rpx">
                <view class="tag">{{ item.topCategoryName }}</view>
                <view class="tag">{{ item.period }} 学时</view>
                <view class="flex-1 text-right text-22rpx text-[var(--primary-color)]">
                  <text>￥</text>
                  <text class="text-26rpx font-bold">{{ item.price }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </ky-scroll-view>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import KyScrollView from '@/components/KyScrollView.vue'
import { httpGet } from '@/utils/uniHttp'
import { useRoute } from 'vue-router'
import { useMessage } from 'wot-design-uni'
const route = useRoute()
const message = useMessage()
const click_course = (item) => {
  message
    .confirm({
      title: '提示',
      msg: '确定要换课吗？',
    })
    .then(async () => {
      const res = await useRequestWrap(() =>
        httpGet('/study/portal/course/change', {
          srcCurseId: route.query.id,
          destCourseId: item.courseId,
        }),
      )
      uni.showToast({
        title: '换课成功',
        icon: 'none',
      })
      uni.navigateBack()
    })
    .catch(() => {})
}
</script>

<style lang="scss" scoped>
.list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  .item {
    overflow: hidden;
    background-color: #fff;
    border-radius: 10rpx;

    .content {
      padding: 19rpx;
    }
  }
}
</style>
