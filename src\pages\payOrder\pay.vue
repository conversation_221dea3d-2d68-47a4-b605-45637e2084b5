<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付',
  },
}
</route>
<template>
  <div class="container">
    <iframe :src="url" frameborder="0" class="w-full h-full"></iframe>
  </div>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'

const url = ref('')
const orderId = ref('')
let timer = null

onLoad(({ url: urlParam, orderId: orderIdParam }) => {
  url.value = decodeURIComponent(urlParam)
  orderId.value = orderIdParam
  timer = setInterval(() => {
    getOrderStatus()
  }, 1000)
})
const getOrderStatus = async () => {
  const res = await useRequestWrap(() =>
    httpGet(`/study/portal/order/getOrderStatus/${orderId.value}`),
  )
  console.log('支付状态 -->', res)
  if (res === '20') {
    clearInterval(timer)
    setTimeout(() => {
      uni.switchTab({ url: '/pages/study/study' })
    }, 1000)
  }
}

onUnload(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style lang="scss" scoped></style>
