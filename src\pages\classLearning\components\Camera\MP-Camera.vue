<template>
  <view class="h-full w-full relative">
    <template v-if="visible">
      <image
        :src="getCdnImageUrl('/course/rline.png')"
        mode="widthFix"
        class="!absolute bottom-0 left-50% -translate-x-50% z-100 w-110%"
      />
      <camera
        mode="normal"
        device-position="front"
        class="h-full w-full"
        @error="handleError"
        @stop="handleStop"
      ></camera>
    </template>
    <div v-else class="h-full flex justify-center items-center text-#999999 text-28rpx">
      相机加载中...
    </div>
  </view>
</template>

<script setup>
import { getCdnImageUrl } from '@/utils/images'
defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

let ctx = null
const visible = ref(false)
onMounted(() => {
  ctx = wx.createCameraContext()
  setTimeout(() => {
    visible.value = true
  }, 100)
})

const handleError = (err) => {
  console.log('相机错误:', err)
  uni.showModal({
    title: '提示',
    content: '相机打开失败，请关闭重试',
    showCancel: false,
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}
const handleStop = (e) => {
  console.log('相机停止', e)
}

// 拍照
const takePhoto = () => {
  return new Promise((resolve, reject) => {
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        resolve(res.tempImagePath)
      },
    })
  })
}

// 录制视频
const startRecord = () => {
  console.log('flag开始录制')
  return new Promise((resolve, reject) => {
    ctx.startRecord({
      success: () => {
        resolve()
      },
      fail: (err) => {
        setTimeout(() => {
          startRecord()
        }, 1000)
        console.log('flag开始录制报错err-->', err)
      },
    })
  })
}
// 停止录制
const stopRecord = () => {
  console.log('flag停止录制')
  return new Promise((resolve, reject) => {
    ctx.stopRecord({
      compressed: true, //压缩视频
      success: (res) => {
        console.log('flag停止录制成功 res-->', res)
        wx.compressVideo({
          src: res.tempVideoPath,
          bitrate: 500,
          fps: 30,
          resolution: 'low',
          success: (resp) => {
            console.log('flag压缩视频成功resp-->', resp)
            resolve(resp.tempFilePath)
          },
          fail: (err) => {
            console.log('flag压缩视频报错err-->', err)
            reject(err)
          },
        })
      },
      fail: (err) => {
        console.log('flag停止录制报错err-->', err)
        reject(err)
      },
    })
  })
}

defineExpose({
  takePhoto,
  startRecord,
  stopRecord,
})
</script>

<style lang="scss" scoped></style>
