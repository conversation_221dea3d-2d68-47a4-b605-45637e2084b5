<template>
  <div class="p-20rpx space-y-20rpx">
    <div
      v-for="item in list"
      class="bg-#fff rounded-15rpx relative px-20rpx py-60rpx"
      @click="goDetails(item)"
    >
      <div
        class="absolute top-0 left-0 rounded-[15rpx_0_30rpx_0] bg-#ff5c0e text-22rpx text-#fff p-[4rpx_30rpx_7rpx_20rpx]"
      >
        已生成
      </div>
      <div class="flex items-cenetr justify-between">
        <div class="text-#666 text-24rpx flex-1">
          学习时间：
          <span class="text-#000">{{ item.beginStudyTime.split(' ')[0] }}</span>
          至
          <span class="text-#000">{{ item.endStudyTime.split(' ')[0] }}</span>
        </div>
        <div class="text-#666 text-24rpx mr-30rpx">
          <span class="text-#ff5c0e text-34rpx font-bold lh-1em">{{ item.total }}</span>
          学时
        </div>
        <wd-icon name="arrow-right" size="20px" color="var(--wot-cell-arrow-color)"></wd-icon>
        <!-- <div class="text-24rpx text-#53b0e9" @click="download(item)">下载学习证明</div> -->
      </div>
    </div>
  </div>

  <wd-popup v-model="visible">
    <wd-img width="100vw" mode="widthFix" :src="src"></wd-img>
  </wd-popup>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'

const { showLoading, hideLoading, toast } = useLoading()
const list = ref([])

const visible = ref(false)
const src = ref('')
const download = async (item) => {
  showLoading()
  try {
    const res = await useRequestWrap(() =>
      httpGet('/study/portal/archive/certificate/imageById', { id: item.id }),
    )
    src.value = res.imageBase64
    visible.value = true
    uni.showToast({
      icon: 'none',
      title: '长按图片保存到手机',
    })
  } finally {
    hideLoading()
  }
}

const goDetails = (item) => {
  uni.navigateTo({ url: `/pages/certificateDetails/gdhbjyw/details?id=${item.id}` })
}

onLoad(({ year }) => {
  console.log(' year-->', year)
  uni.setNavigationBarTitle({ title: year + '年学习证明' })
  const certificateDetails = JSON.parse(uni.getStorageSync('certificateDetails'))
  list.value = certificateDetails
  console.log(' certificateDetails-->', certificateDetails)
})
onUnload(() => {
  uni.removeStorageSync('certificateDetails')
})
</script>

<style lang="scss" scoped></style>
