<template>
  <view v-if="userStore.isLogined" class="container !flex flex-col">
    <Course class="flex-1" />

    <TabBar />
  </view>
</template>

<script lang="ts" setup>
import TabBar from '@/components/TabBar/index.vue'
import Course from '@/pages/study/components/Course.vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()

onShow(() => {
  if (!userStore.isLogined) {
    uni.setStorageSync('redirectUrl', '/pages/study/study')
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
})

onShow(() => {})
</script>

<style scoped lang="scss">
.container {
  display: grid;
  grid-template-rows: auto 1fr;
}

:deep(.top-tabs) {
  .wd-tabs__nav-item-text {
    font-size: 32rpx;
    color: #666666;
    transform: translateY(-2rpx);
  }
  .is-active {
    .wd-tabs__nav-item-text {
      color: var(--primary-color) !important;
    }
  }
}

:deep(.wd-tabs__nav) {
  margin: 5rpx 0;
  background-color: transparent;
}
</style>
