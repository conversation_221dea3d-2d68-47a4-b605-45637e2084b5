<route lang="json5">
{
  style: {
    navigationBarTitleText: '专业培训课程清单',
  },
}
</route>
<template>
  <view class="container overflow-auto">
    <view
      v-if="dataList.length"
      v-for="item in dataList"
      :key="item.id"
      class="bg-#fff p-20rpx my-20rpx flex gap20rpx"
    >
      <kyImage :src="item.cimage" width="300rpx" height="90px" class="flex-shrink-0"></kyImage>
      <view class="flex justify-between flex-col">
        <view class="text-#333 text-30rpx">{{ item.cname }}</view>
        <view class="text-24rpx text-[rgba(153,153,153,1)]">
          已申请
          <text class="text-[var(--primary-color)]">{{ item.cperiod }}</text>
          学时
        </view>
      </view>
    </view>
    <wd-status-tip v-else image="content" tip="暂无内容" />
  </view>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'
import kyImage from '@/components/KyImage.vue'

const dataList = ref([])
const getDataList = async (params) => {
  const res = await useRequestWrap(() => httpGet('/study/portal/archive/creditApplyList', params))
  dataList.value = res
}

onLoad(({ year, topType }) => {
  getDataList({ year, topType })
  uni.setNavigationBarTitle({
    title: uni.getStorageSync('certificateTitle') + '课程培训',
  })
})

onUnload(() => {
  uni.removeStorageSync('certificateTitle')
})
</script>

<style lang="scss" scoped></style>
