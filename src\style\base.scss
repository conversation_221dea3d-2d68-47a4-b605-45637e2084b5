//#ifndef MP-WEIXIN
// @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
// body {
//   font-family: 'Noto Sans SC', sans-serif;
// }
//#endif

//#ifdef MP-WEIXIN
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/notosanssc/v36/k3kXo84MPvpLmixcA63oeALhLOCT-xWNm8Hqd37g1OkDRZe7lR4sg1IzSy-MNbE9VH8V.103.woff2')
    format('woff2');
}
//#endif

// 隐藏滚动条
view {
  ::-webkit-scrollbar {
    display: none;
  }
}

page {
  background-color: #f3f5f9;
}

button::after {
  border: none;
}
/* stylelint-disable-next-line selector-type-no-unknown */
swiper,
/* stylelint-disable-next-line selector-type-no-unknown */
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.uni-tabbar {
  overflow: hidden;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  .uni-tabbar-border {
    background-color: transparent !important;
  }
}
.uni-tabbar__icon {
  width: 24px !important;
  height: 22px !important;
}

.uni-tabbar__item {
  .uni-tabbar__bd {
    transform: translateY(1px);
  }
}
