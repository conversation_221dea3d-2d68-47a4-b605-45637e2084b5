import { create<PERSON>lova } from 'alova'
import AdapterUniapp from '@alova/adapter-uniapp'
import { getEnvBaseUrl } from '@/utils'
import { beforeRequest, responded } from '@/interceptors/request'
import VueHook from 'alova/vue'

// 请求基准地址
const baseUrl = getEnvBaseUrl()

// 创建alova实例
const alovaInstance = createAlova({
  // 基础请求地址
  baseURL: (() => {
    // #ifdef H5
    if (JSON.parse(__VITE_APP_PROXY__)) {
      return '/api'
    }
    // #endif
    return baseUrl
  })(),
  // 使用 uniapp 请求适配器
  ...AdapterUniapp(),
  // 全局的请求拦截器
  beforeRequest,
  // 响应拦截器
  responded,
  // 使用 VueHook 插件
  statesHook: VueHook,
})

// 导出请求方法
export const request = {
  get: <T = any>(url: string, params?: Record<string, any>) => {
    return alovaInstance.Get<T>(url, { params })
  },

  post: <T = any>(url: string, data?: any) => {
    return alovaInstance.Post<T>(url, data)
  },

  put: <T = any>(url: string, data?: any) => {
    return alovaInstance.Put<T>(url, data)
  },

  delete: <T = any>(url: string, data?: any) => {
    return alovaInstance.Delete<T>(url, data)
  },

  upload: <T = any>(url: string, file: UniApp.UploadFileOption) => {
    return alovaInstance.Post<T>(url, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}

export default alovaInstance
