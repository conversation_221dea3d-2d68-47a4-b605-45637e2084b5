<route lang="json5">
{
  style: {
    navigationBarTitleText: '课程',
  },
  enablePullDownRefresh: true,
}
</route>
<template>
  <view class="container">
    <!-- 头部 -->
    <view class="top-wrap flex items-center gap20rpx">
      <Filter @confirm="filterConfirm" />
      <wd-input
        v-model="searchValue"
        use-prefix-slot
        readonly
        custom-class="flex-1 rounded-99px"
        placeholder="搜索你想学的课程"
        @click="toPage('/pages/searchCourse/searchCourse')"
      >
        <template #prefix>
          <view class="flex items-center justify-center">
            <image :src="getCdnImageUrl('/home/<USER>')" mode="" class="w-26rpx h-28rpx" />
          </view>
        </template>
      </wd-input>
      <image
        :src="getCdnImageUrl('/home/<USER>')"
        mode="widthFix"
        class="w42rpx"
        @click="toPage('/pages/shoppingCart/shoppingCart')"
      />
    </view>

    <view class="grid grid-cols-[auto_1fr] h-full overflow-hidden">
      <!-- 课程类型 -->
      <CourseType v-model:category="category" @change="handleChange" />
      <!-- 课程列表 -->
      <view class="w-100% overflow-hidden bg-#fff">
        <KyScrollViewV2
          ref="KyScrollViewV2Comp"
          v-model:current="current"
          :tabList="tabList"
          :apiFun="getCourseList"
        >
          <template #default="{ data }">
            <view class="list">
              <view v-for="item in data" :key="item.id" class="item" @click="click_course(item)">
                <view class="relative">
                  <view
                    v-if="item.classify === '02'"
                    class="absolute top-14rpx left-0 z-1 bg-[var(--primary-color)] text-20rpx text-#fff lh-1em pl-5rpx pt-2rpx pr-8rpx pb-4rpx rounded-[0_99rpx_99rpx_0]"
                  >
                    套餐
                  </view>
                  <ky-image
                    :src="item.cimage"
                    width="230rpx"
                    height="129rpx"
                    class="flex-shrink-0"
                  />
                </view>
                <view class="right">
                  <view class="ellipsis w-full text-28rpx text-#333333">
                    {{ item.cname }}
                  </view>
                  <view class="text-#999999 text-20rpx flex text-nowrap">
                    <view class="w-170rpx ellipsis">适用 {{ item.reportTime }} 等年</view>
                    |
                    {{ item.period }}学时-{{ item.classify }}
                  </view>
                  <view class="flex items-center justify-between w-full">
                    <view class="text-[var(--primary-color)] lh-24rpx">
                      <text class="text-24rpx">￥</text>
                      <text>{{ item.price }}</text>
                    </view>
                    <AddCart :course="item" custom-class="!h-40rpx !w-125rpx !text-20rpx" />
                  </view>
                </view>
              </view>
            </view>
          </template>
        </KyScrollViewV2>
      </view>
    </view>

    <TabBar />
  </view>
</template>

<script setup>
import AddCart from '@/components/AddCart.vue'
import KyImage from '@/components/KyImage.vue'
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import CourseType from './components/CourseType.vue'
import Filter from './components/Filter.vue'
import TabBar from '@/components/TabBar/index.vue'

onShareAppMessage(() => {
  return {
    title: '广东学习网',
    path: '/pages/course/course',
  }
})
onShareTimeline(() => {
  return {
    title: '广东学习网',
    path: '/pages/course/course',
  }
})

const searchValue = ref('')
const category = ref({})
const tabList = computed(() => category.value.children?.map((item) => item.categoryName))
const current = ref(0)
const KyScrollViewV2Comp = ref(null)
watch(current, (val) => {
  nextTick(() => {
    KyScrollViewV2Comp.value.reload(true)
  })
})

// 头部过滤回调
const filterConfirm = (data) => {
  let { ordertype, period } = data
  period = period.split('-')
  pageParams.value.orderType = ordertype
  pageParams.value.periodBegin = period[0]
  pageParams.value.periodEnd = period[1] ?? ''
  KyScrollViewV2Comp.value.reload()
}

const click_course = (item) => {
  uni.navigateTo({
    url: `/pages/course/details?id=${item.courseId}`,
  })
}

const pageParams = ref({
  courseType: '', //id
  periodBegin: '', //学时筛选
  periodEnd: '',
  orderType: '2', //排序字段
})

const handleChange = (value) => {
  pageParams.value.courseType = value
  current.value = 0
  nextTick(() => {
    KyScrollViewV2Comp.value.reload()
  })
}

const getCourseList = async (params) => {
  Object.assign(params, pageParams.value)
  const tabValue = category.value.children[current.value].id
  params.courseType = tabValue === 'all' ? category.value.id : tabValue
  return httpGet('/study/portal/course/page', params)
}
</script>

<style lang="scss" scoped>
.container {
  display: grid;
  grid-template-rows: auto 1fr;
}

.top-wrap {
  // z-index: 1;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);

  :deep(.wd-input) {
    .wd-input__body {
      padding-left: 10px;
      background: #f3f5f9;
      border: none;
      border-radius: 99px;

      .wd-icon {
        background: #f3f5f9;
        transform: translateY(-2px);
      }
    }

    &::after {
      display: none;
    }

    input {
      line-height: 34px;
    }
    .uni-input-placeholder {
      font-size: 20rpx !important;
    }
  }
}

.list {
  box-sizing: border-box;
  padding: 0 25rpx;
  //height: calc(100vh - 56rpx - 59rpx - 55rpx - 44rpx - 170rpx);
  overflow: hidden;

  .item {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 8rpx;
    width: 100%;
    margin: 30rpx 0;
    overflow: hidden;

    :deep(image) {
      border-radius: 10rpx;
    }

    .right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      padding-bottom: 8rpx;
      overflow: hidden;
    }
  }
}
:deep(.z-tabs-conatiner) {
  height: 120rpx !important;
  margin-top: 5rpx;
}
</style>
