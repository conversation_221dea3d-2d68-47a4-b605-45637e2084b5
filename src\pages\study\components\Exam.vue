<template>
  <view class="course">
    <KyScrollViewV2
      v-model:current="current"
      :tabList="tabList"
      :apiFun="getDataList"
      :scrollCount="1"
      :active-style="{
        backgroundColor: 'var(--primary-color)',
        color: '#fff',
        padding: '9rpx 19rpx',
        borderRadius: '99rpx',
        lineHeight: '1',
      }"
    >
      <template #default="{ data }">
        <view class="list px-20rpx gap-20rpx box-border overflow-hidden">
          <view
            v-for="item in data"
            class="w-full item rounded-20rpx overflow-hidden h-194rpx overflow-hidden mb-22rpx"
          >
            <ky-image :src="item.cimage" width="100%" height="194rpx" rounded="20rpx 0 0 20rpx" />
            <view
              class="w-full flex flex-col justify-between px17rpx py20rpx box-border overflow-hidden"
            >
              <view class="text-#333333 text-26rpx font-bold">{{ item.cname }}</view>
              <view class="w-full flex justify-end px5rpx box-border gap-20rpx">
                <wd-button
                  v-if="item.examStatus !== '01'"
                  size="small"
                  plain
                  class="!rounded-10rpx m0 !w120rpx !h42rpx !text-20rpx"
                  @click="toPage('/pages/exam/view', { courseId: item.courseId })"
                >
                  查看答案
                </wd-button>
                <wd-button
                  v-if="item.examStatus === '01'"
                  size="small"
                  plain
                  class="!rounded-10rpx m0 !w120rpx !h42rpx !text-20rpx"
                  @click="toPage('/pages/exam/start', { courseId: item.courseId })"
                >
                  开始考试
                </wd-button>
                <wd-button
                  v-if="item.examStatus === '03'"
                  size="small"
                  plain
                  class="!rounded-10rpx m0 !w120rpx !h42rpx !text-20rpx"
                  @click="toPage('/pages/exam/start', { courseId: item.courseId })"
                >
                  重新考试
                </wd-button>
              </view>
              <view class="sign" :style="{ background: getExamColor(item.examStatus) }">
                {{ EXAM_STATUS[item.examStatus] }}
              </view>
            </view>
          </view>
        </view>
      </template>
    </KyScrollViewV2>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { EXAM_STATUS, getExamColor } from '@/utils/dataStatus'
import { toPage } from '@/utils/toPage'
import { httpPost } from '@/utils/uniHttp'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const current = ref(0)
const tabList = ref(['全部', '未考试', '未通过', '已通过'])
const tabsValue = [[], ['01'], ['03'], ['02']]

const getDataList = async (params) => {
  return httpPost(
    '/study/portal/course/myExamPage',
    { examStatusList: tabsValue[current.value] },
    params,
  )
}
</script>

<style scoped lang="scss">
.course {
  height: 100%;

  :deep(.z-tabs-conatiner) {
    background-color: transparent !important;
    .z-tabs-bottom {
      display: none;
    }
    .z-tabs-item-title-rpx {
      font-size: 24rpx;
    }
  }

  .list {
    .item {
      position: relative;
      display: grid;
      grid-template-columns: 1fr 1fr;
      background-color: #fff;

      .tag {
        padding: 2rpx 4rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: #b6b6b6;
        border: 1px solid #cccccc;
        border-radius: 3px;
      }

      .sign {
        position: absolute;
        top: 0;
        left: 0;
        padding: 10rpx 19rpx;
        font-size: 22rpx;
        color: #fff;
        border-radius: 20rpx 0 20rpx 0;
      }
    }
  }
}
</style>
