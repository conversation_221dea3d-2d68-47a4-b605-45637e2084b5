<template>
  <wd-input v-if="showPassword" v-model="password" :placeholder="placeholder" :type="'text'">
    <template #suffix>
      <view class="h-full p-10rpx" @click="showPassword = !showPassword">
        <image :src="getCdnImageUrl('/login/eye-open.png')" class="w-40rpx h-28rpx" />
      </view>
    </template>
  </wd-input>
  <wd-input v-if="!showPassword" :placeholder="placeholder" v-model="password" :type="'password'">
    <template #suffix>
      <view class="h-full p-10rpx" @click="showPassword = !showPassword">
        <image :src="getCdnImageUrl('/login/eye-close.png')" class="w-40rpx h-17rpx" />
      </view>
    </template>
  </wd-input>
</template>

<script setup>
import { getCdnImageUrl } from '@/utils/images'

defineProps({
  placeholder: {
    type: String,
    default: '请输入密码',
  },
})

const showPassword = ref(false)
const password = defineModel('password', {
  type: String,
  required: true,
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  padding-bottom: 10rpx;
  background: transparent;
  &::after {
    background: #eeeeee;
  }

  .wd-input__icon {
    background: transparent;
  }
  input {
    font-size: 32rpx;
  }
  .uni-input-placeholder {
    font-size: 32rpx !important;
    color: #c7c7c7;
  }
}

:deep(.is-not-empty) {
  &::after {
    background: #eeeeee !important;
  }
}
</style>
