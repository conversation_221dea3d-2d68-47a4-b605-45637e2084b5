// 考试状态
export enum EXAM_STATUS {
  '01' = '未考试',
  '02' = '已通过',
  '03' = '未通过',
}
export const getExamColor = (status: string): string => {
  switch (status) {
    case '01':
      return 'var(--primary-color)'
    case '02':
      return 'var(--primary-color)'
    case '03':
      return '#52b062'
  }
}

// 订单状态
export const getOrderStatus = (status: string): { text: string; color: string } => {
  switch (status) {
    case '01':
      return { text: '未支付', color: 'text-[var(--primary-color)]' }
    case '20':
      return { text: '已支付', color: 'text-[var(--primary-color)]' }
    case '40':
      return { text: '已取消', color: 'text-[var(--primary-color)]' }
    case '80':
      return { text: '未知', color: 'text-[var(--primary-color)]' }
  }
}

// 性别
export enum GENDER {
  'M' = '男',
  'F' = '女',
}

// 教育程度
export enum EDUCATION {
  '博士以上',
  '博士',
  '硕士',
  '本科',
  '大专',
  '高中',
  '中专',
  '初中或以下',
}

// 报名状态 signUpStatus
export enum ENROLL_STATUS {
  '00' = '未上报',
  '01' = '已上报',
  '02' = '已通过',
}

// 学习状态
export enum STUDY_STATUS {
  '00' = '待选课',
  '01' = '待学习',
  '02' = '学习中',
  '03' = '已学完',
  '04' = '已考试',
}
export const getStudyColor = (status: string): string => {
  switch (status) {
    case '01':
      return 'var(--primary-color)'
    case '02':
      return 'var(--primary-color)'
    case '03':
      return '#52b062'
  }
}

// 申报状态
export enum DECLARE_STATUS {
  '01' = '未申报',
  '02' = '申报成功',
  '09' = '申报失败',
}
export const getDeclareColor = (status: string): string => {
  switch (status) {
    case '01':
      return 'var(--primary-color)'
    case '02':
      return '#52B062'
    case '09':
      return '#e43f32'
  }
}

// 考试
export enum QUESTION_TYPE {
  'judge' = '判断',
  'choice_single' = '单选',
  'choice_multiple' = '多选',
}
