<route lang="json5">
{
  style: {
    navigationBarTitleText: '确定并支付',
  },
}
</route>
<template>
  <view class="container">
    <view
      class="text-#FF8C54 text-22rpx text-center py-19rpx bg-#FEEEE8 flex justify-center items-center"
    >
      <text>订单有效期剩余：</text>
      <wd-count-down
        :time="orderInfo.leftTime * 1000 || 0"
        format="HH小时 mm分钟 ss秒"
        class="!text-#FF8C54 !text-22rpx"
      />
    </view>

    <view class="bg-#fff py-36rpx px-29rpx flex justify-between text-24rpx">
      <view>订单编号：{{ orderInfo.order?.ordno }}</view>
      <view>
        订单金额:
        <text class="text-#FF8C54">￥{{ orderInfo.needPayMoney }}</text>
      </view>
    </view>

    <view class="mt-42rpx">
      <view class="mb-29rpx ml-31rpx text-#333333 text-24rpx">选择支付方式</view>
      <wd-radio-group v-model="value" shape="dot">
        <wd-radio
          v-if="orderInfo.dkPay"
          value="onlinePay"
          class="text-28rpx text-#333 px-32rpx py-27rpx"
        >
          <view class="flex items-center gap-21rpx">
            <image src="@/static/pay/dksyt.png" mode="widthFix" class="w-42rpx" />
            <text class="lh-24rpx">收银台</text>
          </view>
        </wd-radio>
        <wd-radio value="balance" class="text-28rpx text-#333 px-32rpx py-27rpx">
          <view class="flex items-center gap-21rpx">
            <image :src="getCdnImageUrl('/my/balance.png')" mode="widthFix" class="w-42rpx" />
            <div class="lh-24rpx">
              余额（当前余额：
              <text class="text-#FF8C54">{{ orderInfo.availableMoney }}</text>
              ）
            </div>
          </view>
        </wd-radio>
      </wd-radio-group>
    </view>

    <view class="px-29rpx mt-70rpx">
      <view class="text-22rpx text-[var(--primary-color)] flex items-center mt-32rpx mb-30rpx">
        <wd-checkbox v-model="checked" shape="square"></wd-checkbox>
        <text @click="openAgreement" class="lh-22rpx">同意课程协议</text>
      </view>
      <wd-button class="!w-690rpx !h-96rpx !text-32rpx" :round="false" @click="handlePay">
        确认支付
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { httpGet, httpPost } from '@/utils/uniHttp'

const checked = ref(true)
const value = ref('balance')
const { toast, showLoading, hideLoading } = useLoading()

let classType = ''
const orderInfo = ref({})
const getOrderInfo = async (id) => {
  const res = await useRequestWrap(() => httpGet('/study/portal/order/getOrderPayInfo', { id }))
  orderInfo.value = res

  // 根据余额情况自动选择支付方式
  if (res.availableMoney >= res.needPayMoney) {
    // 余额足够，选择余额支付
    value.value = 'balance'
  } else if (res.dkPay) {
    // 余额不够且支持在线支付，选择收银台
    value.value = 'onlinePay'
  } else {
    // 余额不够但不支持在线支付，只能选择余额
    value.value = 'balance'
  }
}
const openAgreement = () => {
  uni.setStorageSync(
    'agree',
    JSON.stringify({
      src: 'https://www.gdsjxjy.com/r/cms/gdxxw/xyxy/index.html',
      title: '课程协议',
    }),
  )
  toPage('/pages/agree/agree')
}

const handlePayFn = {
  balance: async () => {
    showLoading()
    const res = await useRequestWrap(
      () =>
        httpPost('/study/portal/order/toPay', {
          orderid: orderInfo.value.order.id,
          paytype: '001',
        }),
      () => hideLoading(),
    )
    toast.success('购买成功')
    setTimeout(() => {
      if (classType === 'ej') {
        uni.navigateTo({ url: '/pages/myClass/myClass' })
      } else {
        uni.switchTab({ url: '/pages/study/study' })
      }
    }, 1000)
  },
  onlinePay: async () => {
    showLoading()
    const res = await useRequestWrap(
      () => httpGet(`/study/portal/order/getDkPayInfo/${orderInfo.value.order.id}?gateway=WAP`),
      () => hideLoading(),
    )
    if (res === null) {
      uni.showToast({
        title: '支付地址错误',
        icon: 'none',
      })
      return
    }
    console.log(' res -->', res)
    window.location.href = res
    // toPage('/pages/payOrder/pay', {
    //   orderId: orderInfo.value.order.id,
    //   url: encodeURIComponent(res),
    // })
  },
}
const handlePay = () => {
  if (!checked.value) {
    toast.show('请勾选同意课程协议！')
    return
  }
  handlePayFn[value.value]()
}

onLoad(({ id, classType: classTypeParam }) => {
  getOrderInfo(id)
  classType = classTypeParam
})
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox__shape) {
  --wot-checkbox-size: 28rpx;
}
</style>
