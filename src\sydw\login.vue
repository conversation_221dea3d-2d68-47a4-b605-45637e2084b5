<route lang="json5">
{
  style: {
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view></view>
</template>

<script setup>
import { useUserStore } from '@/store'
import { httpGet } from '@/utils/uniHttp'
import qs from 'qs'

const userStore = useUserStore()
const login = async (sytoken) => {
  const { accessToken, refreshToken } = await useRequestWrap(() =>
    httpGet(`/admin-svc/user/sydw/login?sytoken=${sytoken}`),
  )
  // 存储token
  userStore.authToken = {
    accessToken,
    refreshToken,
  }
  userStore.getUserInfo().then(() => {
    uni.reLaunch({
      url: '/pages/home/<USER>',
    })
  })
}

onLoad(({ sytoken }) => {
  login(encodeURIComponent(sytoken))
})
</script>

<style lang="scss" scoped></style>
