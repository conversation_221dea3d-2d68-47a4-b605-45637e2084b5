/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/home/<USER>" |
       "/pages/curriculaVariable/curriculaVariable" |
       "/pages/accountSecurity/accountSecurity" |
       "/pages/accountSecurity/changePassword" |
       "/pages/agree/agree" |
       "/pages/apply/apply" |
       "/pages/balanceDetail/balanceDetail" |
       "/pages/certificateDetails/certificateDetails" |
       "/pages/certificateDetails/default" |
       "/pages/certificateDetails/gdhbjyw" |
       "/pages/changeCourse/changeCourse" |
       "/pages/classLearning/classLearning-n" |
       "/pages/classLearning/classLearning" |
       "/pages/classList/classList" |
       "/pages/collectiveInvoice/collectiveInvoice" |
       "/pages/course/course" |
       "/pages/course/details" |
       "/pages/exam/answerSheet" |
       "/pages/exam/start" |
       "/pages/exam/view" |
       "/pages/forgetPassword/forgetPassword" |
       "/pages/home/<USER>" |
       "/pages/home/<USER>" |
       "/pages/learningSelfExamination/details" |
       "/pages/learningSelfExamination/learningSelfExamination" |
       "/pages/login/index" |
       "/pages/login/login" |
       "/pages/my/my" |
       "/pages/myClass/default" |
       "/pages/myClass/details" |
       "/pages/myClass/myClass" |
       "/pages/myClass/txjjw" |
       "/pages/myInfo/myInfo" |
       "/pages/myInvoice/myInvoice" |
       "/pages/myOrder/details" |
       "/pages/myOrder/myOrder" |
       "/pages/news/details" |
       "/pages/news/List" |
       "/pages/news/news" |
       "/pages/payOrder/pay" |
       "/pages/payOrder/payOrder" |
       "/pages/realInfo/realInfo" |
       "/pages/realInfo/realName" |
       "/pages/register/register" |
       "/pages/searchCourse/searchCourse" |
       "/pages/shoppingCart/shoppingCart" |
       "/pages/study/courseList" |
       "/pages/study/declareDetails" |
       "/pages/study/default" |
       "/pages/study/hnndsydw" |
       "/pages/study/study" |
       "/pages/certificateDetails/gdhbjyw/details" |
       "/sydw/login" |
       "/p/course/uncompleted" |
       "/bitMatrix/imageprint";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/home/<USER>" | "/pages/course/course" | "/pages/study/study" | "/pages/my/my"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
