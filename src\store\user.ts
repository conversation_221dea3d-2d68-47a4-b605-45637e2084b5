import { defineStore } from 'pinia'
import { ref } from 'vue'
import { httpGet } from '@/utils/uniHttp'
import { useRequestWrap } from '@/hooks/useRequestWrap'

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<any>({}) //用户信息
    const balance = ref<any>({}) //账户余额
    const isLogined = computed(() => !!authToken.value.accessToken)

    // 账户token
    const authToken = ref({
      accessToken: '',
      refreshToken: '',
    })
    // 清除用户信息
    const clear = () => {
      userInfo.value = {}
      balance.value = {}
      authToken.value = {
        accessToken: '',
        refreshToken: '',
      }
    }
    // 获取用户信息
    const getUserInfo = async () => {
      // 获取用户信息
      const res: any = await useRequestWrap(() => httpGet('/admin/portal/student/info'))
      userInfo.value = res
      if (res.officialPhoto) {
        if (res.officialPhoto.startsWith('http')) {
          userInfo.value.officialPhotoUrl = res.officialPhoto
        } else {
          // 获取免冠半身照地址
          const res2: any = await useRequestWrap(() =>
            httpGet('/file/attachment/' + res.officialPhoto),
          )
          userInfo.value.officialPhotoUrl = res2.url
        }
      }
      // 出生日期
      userInfo.value.birthdayTimestamp = res.birthday ? new Date(res.birthday).getTime() : ''
      // 获取账户余额
      useRequestWrap(() => httpGet('/trade/portal/account/balance')).then((res) => {
        balance.value = res
      })
    }
    // 获取实名信息
    const getRealInfo = async () => {
      // 00未实名 01已实名
      const res = await useRequestWrap(() =>
        httpGet('/admin/portal/user/real/info?id=' + userInfo.value.userId),
      )
      if (res?.realStatus === '00' || !res) {
        return false
      }
      return res
    }
    return {
      userInfo,
      authToken,
      isLogined,
      balance,
      clear,
      getUserInfo,
      getRealInfo,
    }
  },
  { persist: true },
)
