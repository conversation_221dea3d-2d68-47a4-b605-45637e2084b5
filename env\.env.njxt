# 能匠学堂
VITE_APP_TITLE = '能匠学堂'

# 变量必须以 VITE_ 为前缀才能暴露给外部读取
NODE_ENV = 'production'
# 是否去除console 和 debugger
VITE_DELETE_CONSOLE = false
# 是否开启sourcemap
VITE_SHOW_SOURCEMAP = false

# 后台接口地址
#  VITE_SERVER_BASEURL = 'http://192.168.2.85:32402'
# 线上
VITE_SERVER_BASEURL = 'https://m.gdsjxjy.com/api'
VITE_APP_SHOW_LOGO = true

# 能匠学堂
VITE_SITE_NAME = 'njxt'
VITE_SITE_CODE = '88d5301ee22f41e3a1ce7eecb2404be0'

# 服务协议
VITE_SERVICE_PROTOCOL = 'https://www.fzjxjy.com/myhtml/zhucexieyi_njxt.html'