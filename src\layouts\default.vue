<template>
  <ThemeWrap>
    <slot />
    <wd-toast />
    <wd-message-box />
  </ThemeWrap>
</template>

<script setup>
import ThemeWrap from '@/components/ThemeWrap.vue'
import { shouldHideNavBar } from '@/utils/browser'

onShow(() => {
  // #ifdef H5
  nextTick(() => {
    if (shouldHideNavBar()) {
      document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
    }
  })
  // #endif
})
</script>
