<template>
  <view class="h-full w-full flex">
    <z-paging-swiper :fixed="false" class="h-full flex-1 flex">
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
      <template #top>
        <z-tabs
          ref="tabsRef"
          :list="tabList"
          :current="current"
          :scroll-count="scrollCount"
          :bottom-space="bottomSpace"
          :active-style="activeStyle"
          active-color="var(--primary-color)"
          slidable="always"
          @change="tabsChange"
        />
      </template>
      <!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面  -->
      <swiper
        class="h-full"
        :current="current"
        :duration="duration"
        @transition="swiperTransition"
        @animationfinish="swiperAnimationfinish"
      >
        <swiper-item class="swiper-item" v-for="(item, index) in tabList" :key="index">
          <SwiperListItem
            ref="listItemRef"
            :tabIndex="index"
            :currentIndex="current"
            :apiFun="apiFun"
          >
            <template #default="{ data }">
              <slot :data="data"></slot>
            </template>
          </SwiperListItem>
        </swiper-item>
      </swiper>
    </z-paging-swiper>
  </view>
</template>

<script setup>
import SwiperListItem from './SwiperListItem.vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})
const props = defineProps({
  tabList: {
    type: Array,
    default: () => [],
  },
  apiFun: {
    type: Function,
    default: () => {},
  },
  scrollCount: {
    type: Number,
    default: 5,
  },
  bottomSpace: {
    type: Number,
    default: 0,
  },
  activeStyle: {
    type: Object,
    default: () => {},
  },
  duration: {
    type: Number,
    default: 200,
  },
})
const emit = defineEmits(['change'])

const listItemRef = ref(null)
const current = defineModel('current') // tabs组件的current值，表示当前活动的tab选项
const tabsRef = ref(null)

// tabs通知swiper切换
function tabsChange(index) {
  current.value = index
  emit('change', index)
}
// swiper滑动中
function swiperTransition(e) {
  tabsRef.value.setDx(e.detail.dx)
}
// swiper滑动结束
function swiperAnimationfinish(e) {
  current.value = e.detail.current
  tabsRef.value.unlockDx()
}

defineExpose({
  reload: (type) => {
    listItemRef.value?.[current.value]?.reload(type)
  },
  refresh: () => {
    listItemRef.value?.[current.value]?.refresh()
  },
})
</script>

<style lang="scss" scoped>
:deep(.z-tabs-item-title-rpx) {
  white-space: nowrap;
  transition: 0.2s;
}
:deep(.z-tabs-bottom) {
  bottom: 0 !important;
}
:deep(.z-tabs-list) {
  flex: none !important;
}
</style>
