<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人资料',
  },
}
</route>
<template>
  <view class="container !overflow-auto">
    <wd-cell-group border>
      <wd-cell title="头像" center>
        <template #default>
          <view class="value-wrap" @click="editAvatar">
            <ky-image :src="userInfo.avatar" width="100rpx" height="100rpx" rounded="99px" />
          </view>
        </template>
      </wd-cell>
      <wd-cell title="用户名" center>
        <template #default>
          <view class="value-wrap">
            <text>{{ userInfo.username }}</text>
          </view>
        </template>
      </wd-cell>
      <wd-cell title="免冠半身照" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('officialPhotoUrl', '免冠半身照')">
            <text>{{ userInfo.officialPhotoUrl ? '已上传' : '未上传' }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
    </wd-cell-group>

    <wd-cell-group border class="mt-20rpx">
      <wd-cell title="姓名" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('realName', '姓名')">
            <text>{{ userInfo.realName }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
      <wd-cell title="身份证号" center>
        <template #default>
          <view class="value-wrap">
            <text>{{ userInfo.idCard }}</text>
          </view>
        </template>
      </wd-cell>
      <wd-cell title="性别" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('sex', '性别')">
            <text>{{ GENDER[userInfo.sex] }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
      <wd-cell title="出生年月" center>
        <template #default>
          <view class="value-wrap">
            <wd-datetime-picker
              v-model="userInfo.birthdayTimestamp"
              type="date"
              :formatter="formatter"
              :min-date="Date.now() - 100 * 365 * 24 * 60 * 60 * 1000"
              :max-date="Date.now()"
              @confirm="handleBirthdayConfirm"
            />
          </view>
        </template>
      </wd-cell>
      <wd-cell title="教育程度" center>
        <template #default>
          <view class="value-wrap">
            <wd-picker
              :columns="educationColumns"
              v-model="userInfo.education"
              @confirm="handleEducationConfirm"
            />
          </view>
        </template>
      </wd-cell>
      <wd-cell title="单位名称" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('orgName', '单位名称')">
            <text>{{ userInfo.orgName }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
    </wd-cell-group>

    <wd-cell-group border class="mt-20rpx">
      <wd-cell title="通讯地址" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('address', '通讯地址')">
            <text>{{ userInfo.address }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
      <wd-cell title="邮编" center>
        <template #default>
          <view class="value-wrap" @click="handleEdit('postcode', '邮编')">
            <text>{{ userInfo.postcode }}</text>
            <wd-icon name="arrow-right" size="16px" color="var(--wot-cell-arrow-color)"></wd-icon>
          </view>
        </template>
      </wd-cell>
    </wd-cell-group>

    <Edit ref="editCom" :edit-field="editField" />
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import { useUploadFile } from '@/hooks/useUploadFile'
import { useUserStore } from '@/store/user'
import { EDUCATION, GENDER } from '@/utils/dataStatus'
import dayjs from 'dayjs'
import Edit from './components/Edit.vue'
import { editUserInfo } from './helpr'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const { showLoading, hideLoading } = useLoading()

// 修改头像
const { upload } = useUploadFile(1)
const editAvatar = async () => {
  const { url } = await upload()
  showLoading('修改中...')
  editUserInfo({ avatar: url }, async () => {
    await userStore.getUserInfo()
    hideLoading()
  })
}
// 修改出生年月
const formatter = (type, value) => {
  switch (type) {
    case 'year':
      return value + '年'
    case 'month':
      return value + '月'
    case 'date':
      return value + '日'
  }
}
const handleBirthdayConfirm = ({ value }) => {
  const birthday = dayjs(value).format('YYYY-MM-DD')
  editUserInfo({ birthday }, async () => {
    await userStore.getUserInfo()
  })
}
// 修改教育程度
const educationColumns = Object.keys(EDUCATION)
  .filter((key) => isNaN(Number(key)))
  .map((label, index) => ({
    label,
    value: index,
  }))
const handleEducationConfirm = ({ value }) => {
  editUserInfo({ education: value }, async () => {
    await userStore.getUserInfo()
  })
}

// 编辑弹窗
const editCom = ref(null)
const editField = reactive({
  field: '',
  title: '',
  value: '',
})
const handleEdit = (field, title) => {
  editField.field = field
  editField.title = title
  editField.value = userInfo.value[field]
  console.log('editField -->', editField)
  editCom.value.open()
}
</script>

<style lang="scss" scoped>
.value-wrap {
  display: flex;
  gap: 10rpx;
  align-items: center;
  justify-content: flex-end;
  color: #999999;
  :deep(.wd-picker__cell) {
    padding: 0 !important;
    &::after {
      display: none !important;
    }

    .wd-picker__value {
      margin-right: 8rpx;
      color: #999999;
    }
  }
}
</style>
