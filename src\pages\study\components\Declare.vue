<template>
  <view class="course">
    <KyScrollViewV2
      v-model:current="current"
      :tabList="tabList"
      :apiFun="getDataList"
      :scrollCount="1"
      :active-style="{
        backgroundColor: 'var(--primary-color)',
        color: '#fff',
        padding: '9rpx 19rpx',
        borderRadius: '99rpx',
        lineHeight: '1',
      }"
    >
      <template #default="{ data }">
        <view class="list px-20rpx gap-20rpx box-border overflow-hidden space-y-23rpx">
          <view
            v-for="item in data"
            class="w-full item rounded-20rpx overflow-hidden h-399rpx overflow-hidden relative"
            @click="toPage('/pages/study/declareDetails', { id: item.id })"
          >
            <ky-image :src="item.cimage" width="100%" height="100%" rounded="20rpx 0 0 20rpx" />
            <view
              class="absolute bottom-0 left-0 w-full p-19rpx text-#fff"
              style="background: rgba(0, 0, 0, 0.4)"
            >
              <view class="text-26rpx">{{ item.cname }}</view>
              <view class="flex gap-10rpx mt-20rpx">
                <view class="tag">{{ item.topCategoryName }}</view>
                <view class="tag">{{ item.cperiod }}学时</view>
              </view>
            </view>

            <view
              class="absolute bottom-0 right-0 text-#fff text-22rpx rounded-[20rpx_0_0_0] px-19rpx py-10rpx"
              :style="{ backgroundColor: getDeclareColor(item.creditStatus) }"
            >
              {{ DECLARE_STATUS[item.creditStatus] }}
            </view>
          </view>
        </view>
      </template>
    </KyScrollViewV2>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { DECLARE_STATUS, getDeclareColor } from '@/utils/dataStatus'
import { toPage } from '@/utils/toPage'
import { httpPost } from '@/utils/uniHttp'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const current = ref(0)
const tabList = ref(['全部', '待申报', '已申报'])
const tabsValue = [[], ['01', '09'], ['02']]

const getDataList = (params) => {
  return httpPost(
    '/study/portal/credit/apply/page',
    { creditStatusList: tabsValue[current.value] },
    params,
  )
}
</script>

<style scoped lang="scss">
.course {
  height: 100%;
  :deep(.z-tabs-conatiner) {
    background-color: transparent !important;
    .z-tabs-bottom {
      display: none;
    }
    .z-tabs-item-title-rpx {
      font-size: 24rpx;
    }
  }

  .list {
    .item {
      position: relative;
      display: flex;
      background-color: #fff;

      .tag {
        color: #fff;
        border-color: #fff;
        opacity: 0.8;
      }

      .sign {
        position: absolute;
        top: 0;
        left: 0;
        padding: 10rpx 20rpx 8rpx;
        font-size: 22rpx;
        color: #fff;
        border-radius: 20rpx 0 20rpx 0;
      }
    }
  }
}
</style>
