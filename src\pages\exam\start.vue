<route lang="json5">
{
  style: {
    navigationBarTitleText: '考试',
  },
}
</route>
<template>
  <view v-if="!loading" class="container">
    <view class="px-15px h-52px flex justify-between items-center">
      <wd-count-down ref="countDownComp" :time="time * 60 * 1000" @finish="toAnswerSheet(true)" />
      <view class="flex gap10px">
        <text class="text-15px text-[var(--primary-color)]" @click="toAnswerSheet(false)">
          答题卡
        </text>
        <text class="text-15px text-[var(--primary-color)]" @click="handlePause">暂停</text>
      </view>
    </view>

    <view class="p-30rpx text-30rpx text-#333333">
      <view>
        {{ currentIndex + 1 }}、[{{ QUESTION_TYPE[currentQuestion.type] }}]
        {{ currentQuestion.content }}
      </view>

      <view>
        <!-- 多选题 -->
        <wd-checkbox-group
          v-if="currentQuestion.type === 'choice_multiple'"
          v-model="currentQuestion.userAnswer"
          shape="square"
          class="!bg-transparent px-30rpx py-20rpx"
        >
          <wd-checkbox v-for="item in currentQuestion.optionList" :modelValue="item.name">
            {{ item.name }}、{{ item.content }}
          </wd-checkbox>
        </wd-checkbox-group>
        <!-- 单选题 -->
        <wd-radio-group
          v-else-if="currentQuestion.type === 'choice_single'"
          v-model="currentQuestion.userAnswer"
          class="!bg-transparent px-30rpx py-20rpx"
          shape="dot"
          inline
        >
          <wd-radio v-for="item in currentQuestion.optionList" :value="item.name">
            {{ item.name }}、{{ item.content }}
          </wd-radio>
        </wd-radio-group>
        <!-- 判断题 -->
        <wd-radio-group
          v-else-if="currentQuestion.type === 'judge'"
          v-model="currentQuestion.userAnswer"
          class="!bg-transparent px-30rpx py-20rpx"
          shape="dot"
          inline
        >
          <wd-radio v-for="item in currentQuestion.optionList" :value="item.name">
            {{ item.content }}
          </wd-radio>
        </wd-radio-group>
      </view>
    </view>

    <view class="absolute bottom-0 left-0 right-0 flex justify-between">
      <wd-button class="flex-1 !rounded-0 !h-46px" plain @click="prevQuestion">上一题</wd-button>
      <wd-button
        v-if="currentIndex < questionList.length - 1"
        class="flex-1 !rounded-0 !h-46px"
        @click="nextQuestion"
      >
        下一题
      </wd-button>
      <wd-button v-else class="flex-1 !rounded-0 !h-46px" @click="toAnswerSheet(false)">
        答题卡
      </wd-button>
    </view>
  </view>

  <ky-dialog
    v-model:visible="pauseVisible"
    :showCancelButton="false"
    confirmText="继续答题"
    @confirm="handleContinue"
  >
    <view class="text-#333 text-30rpx py-20rpx">暂停答题</view>
  </ky-dialog>
</template>

<script setup>
import { QUESTION_TYPE } from '@/utils/dataStatus'
import { httpGet } from '@/utils/uniHttp'
import kyDialog from '@/components/KyDialog.vue'
import { toPage } from '@/utils/toPage'
import { useExamStore } from '@/store'

const examStore = useExamStore()
const { toast, showLoading, hideLoading, loading } = useLoading()
let courseId = ''
let classType = ''
const questionList = ref([])
const currentIndex = ref(0)
const currentQuestion = computed(() => questionList.value[currentIndex.value] || {})
const time = ref(0)

// 倒计时
const countDownComp = ref(null)
const pauseVisible = ref(false)
const handlePause = () => {
  countDownComp.value.pause()
  pauseVisible.value = true
}
const handleContinue = () => {
  pauseVisible.value = false
  countDownComp.value.start()
}
// 答题卡
const toAnswerSheet = (flag) => {
  const answerList = questionList.value.map((item) => {
    return {
      id: item.id,
      userAnswer: Array.isArray(item.userAnswer) ? item.userAnswer.join(',') : item.userAnswer,
      type: item.type,
    }
  })
  examStore.setAnswerList(answerList)
  toPage('/pages/exam/answerSheet', { courseId, flag, classType })
}

// 底部按钮
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else {
    toast.show('当前已经是第一题')
  }
}
const nextQuestion = () => {
  if (currentIndex.value < questionList.value.length - 1) {
    currentIndex.value++
  } else {
    toast.show('当前已经是最后一题')
  }
}

const getExamResult = async () => {
  showLoading('加载中...')
  const res = await useRequestWrap(
    () => {
      if (classType === 'ejClass') {
        return httpGet('/study/portal/exam/planExam', {
          planId: courseId,
        })
      } else {
        return httpGet('/study/portal/exam/courseExam', {
          courseId,
        })
      }
    },
    (res) => {
      hideLoading()
      if (res.code === 1) {
        loading.value = true
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
  )
  time.value = +res.config.time
  res.questionList.forEach((item) => {
    if (item.type === 'choice_multiple') {
      item.userAnswer = []
    } else {
      item.userAnswer = ''
    }
  })
  questionList.value = res.questionList
}

onLoad((options) => {
  courseId = options.courseId
  classType = options.classType
  getExamResult()
})
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox) {
  display: flex;
  align-items: center;
  .wd-checkbox__shape {
    flex-shrink: 0;
  }
  .wd-checkbox__txt {
    white-space: wrap;
  }
}

:deep(.wd-radio-group) {
  display: flex;
  flex-direction: column;
  .wd-radio {
    margin-bottom: 10px;
    text-align: left;
    &:last-child {
      margin-bottom: 0;
    }
    .wd-radio__label {
      margin-left: 10rpx;
      line-height: 35rpx;
    }
  }
}

:deep(.wd-count-down) {
  font-size: 22px;
  font-weight: bold;
}
</style>
