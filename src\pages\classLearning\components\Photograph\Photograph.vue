<template>
  <view>
    <Ky-dialog
      v-model:visible="visible"
      class="dialog"
      @closed="handleClosed"
      @opened="handleOpened"
    >
      <view class="text-38rpx text-#fb362e font-bold">
        {{ props.type === '1' ? '请规范拍照，否则退回重学' : '视频采集，人工审核' }}
      </view>
      <view class="wrapper">
        <view v-if="!photo && !video && visible" class="h-full w-full">
          <!-- #ifdef H5 -->
          <H5Camera ref="cameraCom"></H5Camera>
          <!-- #endif -->
          <!-- #ifdef MP-WEIXIN -->
          <MPCamera ref="cameraCom"></MPCamera>
          <!-- #endif -->
          <!-- #ifdef APP-PLUS -->
          <!--          <AppCamera ref="cameraCom"></AppCamera>-->
          <!-- #endif -->
        </view>
        <image v-else-if="photo" :src="photo" mode="widthFix" />
        <video
          v-else
          :src="video"
          mode="widthFix"
          class="w-full h-full"
          disablePictureInPicture
          playsinline
          webkit-playsinline
          x5-playsinline
          x5-video-player-type="h5"
          x-webkit-airplay="deny"
          controlsList="nodownload noplaybackrate"
        />
      </view>
      <template #footer>
        <view class="footer">
          <wd-button v-if="!photo && !video" plain @click="handleCancel">关闭</wd-button>
          <wd-button v-else plain @click="handleReTakePhoto">
            {{ props.type === '1' ? '重拍' : '重录' }}
          </wd-button>
          <view v-if="!photo && !video">
            <wd-button v-if="!isRecording" @click="handleTakePhoto">
              {{ props.type === '1' ? '拍摄' : '开始录制' }}
            </wd-button>
            <wd-button v-else disabled>录制中({{ recordingTime }}s)</wd-button>
          </view>
          <wd-button v-else @click="handleSubmit">提交</wd-button>
        </view>
      </template>
    </Ky-dialog>
  </view>
  <wd-toast />
</template>

<script setup lang="ts">
import KyDialog from '@/components/KyDialog.vue'
import { httpUpload } from '@/utils/uniHttp'
import qs from 'qs'
import H5Camera from '../Camera/H5-Camera.vue'
import MPCamera from '../Camera/MP-Camera.vue'
import AppCamera from '../Camera/App-Camera.nvue'
// import Camera from './components/Camera.vue'
defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

// type: 1:拍照 2:录制视频
const props = defineProps<{
  type: '1' | '2'
  cw: any
}>()
const emit = defineEmits(['verify', 'opened'])
const { toast, showLoading, hideLoading } = useLoading()

const visible = defineModel('visible')
const cameraCom = ref(null)
const photo = ref('')
const video = ref('')
// 拍照 & 录制视频
const isRecording = ref(false)
const recordingTime = ref(6)
let recordTimer = null
const handleTakePhoto = async () => {
  console.log('cameraCom', cameraCom.value)

  try {
    if (props.type === '1') {
      photo.value = await cameraCom.value.takePhoto()
    } else {
      isRecording.value = true
      cameraCom.value.startRecord()
      recordTimer = setInterval(async () => {
        recordingTime.value--
        if (recordingTime.value <= 0) {
          clearInterval(recordTimer)
          isRecording.value = false
          showLoading('视频处理中...')
          try {
            video.value = await cameraCom.value.stopRecord()
          } catch (error) {
            uni.showModal({
              title: '提示',
              content: error.errMsg,
              showCancel: false,
              success: () => {
                visible.value = false
              },
            })
          } finally {
            hideLoading()
          }
        }
      }, 1000)
    }
  } catch (error) {
    console.log('拍照错误-->', error)
  }
}
// 重拍
const handleReTakePhoto = async () => {
  photo.value = ''
  video.value = ''
  isRecording.value = false
  recordingTime.value = 6
}

// 提交 1拍照 3视频
const handleSubmit = async () => {
  clearTimeout(timer)
  showLoading('提交中...')
  const params = {
    coursewareId: props.cw.resId,
    courseId: props.cw.courseId,
    lessonLocation: props.cw.lessonLocation,
    classify: props.type === '1' ? 1 : 3,
  }
  const res = await httpUpload(
    `/study/portal/play/verify/saveRecordFile?${qs.stringify(params)}`,
    props.type === '1' ? photo.value : video.value,
  )
  if (res.code !== 0) {
    emit('verify', false)
    visible.value = false
    hideLoading()
    toast.show(res.msg)
    return
  }
  emit('verify', true, res.data)
  visible.value = false
  hideLoading()
  if (props.type === '1') {
    toast.show('验证成功')
  } else {
    toast.show('上传成功')
  }
}
const handleCancel = () => {
  visible.value = false
  emit('verify', false)
}

let timer = null
const handleOpened = () => {
  emit('opened')
  if (props.type === '1') {
    timer = setTimeout(() => {
      if (visible.value === true) {
        emit('verify', false)
        toast.show(props.type === '1' ? '未上传照片！' : '未上传视频！')
        visible.value = false
      }
    }, 60000)
  }
}
const handleClosed = () => {
  cameraCom.value?.stopRecord()
  photo.value = ''
  video.value = ''
  clearTimeout(timer)
  timer = null
  clearInterval(recordTimer)
  recordTimer = null
  isRecording.value = false
  recordingTime.value = 6
}
</script>

<style lang="scss" scoped>
:deep(.dialog) {
  .wd-message-box__container {
    width: 90vw !important;
  }

  .wd-message-box__body {
    padding: 40rpx 30rpx;
  }

  .footer {
    display: grid !important;
    grid-template-columns: 0.45fr 1fr;
    gap: 20rpx;
    margin-top: 24rpx;

    .wd-button {
      width: 100% !important;
      min-width: 0px !important;
      padding: 0 !important;
      margin: 0px !important;
    }
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 3/4;
  margin: 0 auto;
  margin-top: 20rpx;
  overflow: hidden;
  background: #000;
  border-radius: 10rpx;
}
</style>
