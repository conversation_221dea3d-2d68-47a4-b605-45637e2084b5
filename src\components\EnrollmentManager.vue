<template>
  <view class="flex justify-center mt-20rpx gap-20rpx">
    <wd-button v-if="item.publish === '01'" class="m0" @click="handlePreSignUp(item)">
      预报名
    </wd-button>
    <wd-button
      v-else-if="SignUpBtnVisible && item.publish === '02'"
      class="m0"
      @click="handleSignUp(item)"
    >
      立即报名
    </wd-button>
    <wd-button v-else-if="item.status === '20'" class="m0" @click="toPay">进行缴费</wd-button>
    <slot></slot>
    <wd-toast />
  </view>

  <!-- 预报名弹框 -->
  <KyDialog
    v-model:visible="preSignUpVisible"
    title="预报名"
    @closed="handlePreSignClose"
    @confirm="handlePreSignUpConfirm"
  >
    <view>
      <view class="flex items-center gap20rpx">
        <view class="flex justify-between w-90rpx">
          <text>姓</text>
          <text>名</text>
        </view>
        <wd-input v-model="preSignUpForm.visitorName" placeholder="请输入姓名" class="flex-1" />
      </view>
      <view class="flex items-center gap20rpx">
        <view class="flex justify-between w-90rpx">
          <text>手</text>
          <text>机</text>
          <text>号</text>
        </view>
        <wd-input v-model="preSignUpForm.visitorMobile" placeholder="请输入手机号" class="flex-1" />
      </view>
    </view>
  </KyDialog>
  <!-- 立即报名弹框 -->
  <NoticeBox
    v-model:visible="signUpVisible"
    title="报名须知"
    confirmText="确定报名"
    @confirm="handleSignUpConfirm"
  />
</template>

<script setup lang="ts">
// publish 02=已发布 01=预发布 00=未发布
defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

import KyDialog from '@/components/KyDialog.vue'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'
import NoticeBox from './NoticeBox.vue'
const props = defineProps<{
  item: any
  classType?: string | undefined
}>()
const emit = defineEmits(['preSignOpen', 'preSignClose'])
const SignUpBtnVisible = computed(() => {
  const expired = props.item.endTime != null && new Date(props.item.endTime) < new Date()
  const maxNum = props.item.maxNum || 0
  const userCount = props.item.userCount || 0
  const isFull = maxNum > 0 && maxNum <= userCount
  return props.item.status === '00' && !expired && !isFull
})

const toast = useToast()
const planId = ref('')
// 预报名
const preSignUpVisible = ref(false)
const { formData: preSignUpForm, reset } = useFormDate({
  visitorName: '',
  visitorMobile: '',
})
const handlePreSignUp = (item: any) => {
  planId.value = item.id
  preSignUpVisible.value = true
  emit('preSignOpen')
}
const handlePreSignUpConfirm = async () => {
  preSignUpForm.value.planId = planId.value
  await useRequestWrap(() => httpGet('/study/portal/plan/sign/up', preSignUpForm.value))
  preSignUpVisible.value = false
  toast.success('预报名成功')
}
const handlePreSignClose = () => {
  reset()
  emit('preSignClose')
}

// 立即报名
const signUpVisible = ref(false)
const handleSignUp = (item: any) => {
  planId.value = item.id
  signUpVisible.value = true
}
const handleSignUpConfirm = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/order/savePlanOrder', { planId: planId.value }),
  )
  toPage('/pages/payOrder/payOrder', { id: res, classType: props.classType })
  signUpVisible.value = false
}

// 进行缴费
const toPay = () => {
  toPage('/pages/payOrder/payOrder', { id: props.item.orderId, classType: props.classType })
}
</script>

<style lang="scss" scoped></style>
