<template>
  <view>
    <wd-message-box selector="ky-dialog" :custom-class="customClass">
      <slot></slot>
      <slot v-if="$slots.footer" name="footer"></slot>
      <view class="footer" v-else>
        <wd-button v-if="showConfirmButton" @click="confirm">{{ props.confirmText }}</wd-button>
        <wd-button v-if="showCancelButton" plain @click="cancel">{{ props.cancelText }}</wd-button>
      </view>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

interface Props {
  title?: string
  dialogOptions?: any
  confirmText?: string
  cancelText?: string
  width?: string
  showConfirmButton?: boolean
  showCancelButton?: boolean
  customClass?: string
}
const props = withDefaults(defineProps<Props>(), {
  title: '',
  dialogOptions: () => ({}),
  confirmText: '确定',
  cancelText: '取消',
  width: '300px',
  showConfirmButton: true,
  showCancelButton: true,
  customClass: '',
})
const customClass = computed(() => {
  return 'ky-dialog ' + props.customClass
})
const emits = defineEmits(['confirm', 'cancel', 'opened', 'closed'])

const message = useMessage('ky-dialog')
const visible = defineModel('visible')

let isInit = true

onMounted(() => {
  isInit = false
})

watch(
  visible,
  (val) => {
    if (val) {
      setTimeout(() => {
        emits('opened')
      }, 300)
      message
        .confirm({
          title: props.title,
          closeOnClickModal: false,
          ...props.dialogOptions,
        })
        .then(() => {
          visible.value = false
          emits('confirm')
        })
        .catch((action) => {
          visible.value = false
          emits('cancel')
          setTimeout(() => {
            emits('closed')
          }, 300)
        })
    } else {
      if (isInit) return
      message.close()
      setTimeout(() => {
        emits('closed')
      }, 100)
    }
  },
  { immediate: true },
)

const confirm = () => {
  emits('confirm')
}

const cancel = () => {
  emits('cancel')
  visible.value = false
}
</script>

<style lang="scss" scoped>
:deep(.wd-message-box__container) {
  --wot-message-box-width: v-bind(width) !important;
}

:deep(.ky-dialog) {
  .wd-message-box__body {
    padding: 50rpx 50rpx 45rpx 50rpx !important;
  }

  .wd-message-box__title {
    padding: 0rpx;
    margin-bottom: 35rpx;
    font-size: 34rpx;
    font-weight: bold;
    color: #333333;
  }

  .wd-message-box__actions {
    display: none !important;
  }

  .wd-message-box__content {
    max-height: inherit;
  }

  .footer {
    display: flex;
    gap: 40rpx;
    width: 100%;
    height: 81rpx;
    margin-top: 40rpx;
    overflow: hidden;
    .wd-button {
      width: calc(50% - 20rpx);
      min-width: 0rpx !important;
      height: 80rpx;
    }
  }
}
</style>
