<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="container bg-#ffffff flex flex-col">
    <view
      class="h-44px flex items-center gap-20rpx px-27rpx justify-between relative"
      :style="{ paddingTop: 'var(--status-bar-height)' }"
    >
      <!-- 阴影壳 -->
      <view
        style="box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.2)"
        :style="{ opacity: titleOpacity }"
        class="absolute top-0 left-0 w-full h-full"
      ></view>

      <image
        :src="getCdnImageUrl('/common/arrow_left.png')"
        mode="widthFix"
        class="w-18rpx relative z-10"
        @click="back"
      />

      <view
        class="flex-1 text-#333333 text-30rpx font-bold text-center ellipsis"
        :style="{ opacity: titleOpacity, paddingRight: 'var(--capsule-width)' }"
      >
        <text class="ellipsis">
          {{ newsDetails.title }}
        </text>
      </view>

      <!-- 分享 -->
      <!-- <image src="@img/common/share.png" mode="widthFix" class="w-36rpx" /> -->
    </view>
    <scroll-view
      scroll-y
      :upper-threshold="titleBoxHeight"
      class="px-20rpx pb-20rpx pt-10rpx overflow-auto box-border flex-1"
      @scroll="handleScroll"
      @scrolltolower="handleScrollEnd"
    >
      <view class="title-box">
        <view class="font-bold text-40rpx text-#333333 mx-15rpx">{{ newsDetails.title }}</view>
        <view class="flex items-center gap80rpx text-22rpx text-#999999 my-40rpx mx-15rpx">
          <view class="flex items-center gap7rpx">
            <image :src="getCdnImageUrl('/common/time.png')" mode="widthFix" class="w-22rpx" />
            <text class="lh-22rpx">{{ dayjs(newsDetails.createTime).format('MM-DD') }}</text>
          </view>
          <view class="flex items-center gap7rpx">
            <image :src="getCdnImageUrl('/common/view.png')" mode="widthFix" class="w-25rpx" />
            <text class="lh-22rpx">{{ newsDetails.viewcount }}人浏览</text>
          </view>
        </view>
      </view>
      <view class="content">
        <up-parse :content="newsDetails.content"></up-parse>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { getCdnImageUrl } from '@/utils/images'
import { httpGet } from '@/utils/uniHttp'
import dayjs from 'dayjs'

const newsDetails = ref({})

const getNewsDetails = async (id) => {
  const res = await useRequestWrap(() => httpGet(`/cms/portal/article/content/${id}`))
  newsDetails.value = res
  setTimeout(() => {
    getTitleBoxHeight()
  }, 100)
}

const titleBoxHeight = ref(0)
const titleOpacity = ref(0)
const handleScroll = (e) => {
  titleOpacity.value = e.detail.scrollTop / titleBoxHeight.value
}
const handleScrollEnd = () => {
  setTimeout(() => {
    titleOpacity.value = 1
  }, 180)
}
const getTitleBoxHeight = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.title-box')
    .boundingClientRect((data) => {
      titleBoxHeight.value = Math.ceil(data.height)
    })
    .exec()
}

const back = () => {
  uni.navigateBack()
}

onLoad(({ id }) => {
  getNewsDetails(id)
})
</script>

<style lang="scss" scoped>
.content {
  :deep(img) {
    max-width: 100% !important;
  }
}
</style>
