<route lang="json5">
{
  style: {
    navigationBarTitleText: '实名认证',
  },
}
</route>
<template>
  <view class="container">
    <view v-if="realInfo" class="bg-#fff p-30rpx my-20rpx">实名认证已通过</view>
    <view v-else class="bg-#fff p-30rpx my-20rpx">还没有进行实名认证，请完成认证</view>

    <wd-cell-group border class="my-20rpx">
      <wd-cell title="姓名" center>
        <text class="text-#999">{{ realInfo.realname || '-' }}</text>
      </wd-cell>
      <wd-cell title="身份证" center>
        <text class="text-#999">{{ realInfo.cardno || '-' }}</text>
      </wd-cell>
    </wd-cell-group>

    <view v-if="!realInfo" class="p-30rpx mt-30rpx">
      <wd-button class="w-full" @click="toPage('/pages/realInfo/realName')" size="large">
        实名认证
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store'
import { toPage } from '@/utils/toPage'
const userStore = useUserStore()
const { showLoading, hideLoading } = useLoading()

const realInfo = ref({})
const getRealInfo = async () => {
  showLoading()
  realInfo.value = await userStore.getRealInfo()
  hideLoading()
}
getRealInfo()
</script>

<style lang="scss" scoped></style>
