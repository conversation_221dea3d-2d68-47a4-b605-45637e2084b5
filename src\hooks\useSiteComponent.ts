import { shallowRef, Component } from 'vue'

/**
 * 根据环境加载不同组件的Hook
 * @param modules - 通过import.meta.glob获取的模块映射
 * @param siteName - 环境名称，默认使用VITE_SITE_NAME
 * @returns 包含组件引用的对象
 */
export function useSiteComponent(
  modules: Record<string, () => Promise<{ default: Component }>>,
  siteName: string = import.meta.env.VITE_SITE_NAME || 'default',
) {
  const component = shallowRef<Component | null>(null)

  const allPaths = Object.keys(modules)
  const siteFileName = `${siteName}.vue`
  const defaultFileName = 'default.vue'

  const matchedPath = allPaths.find((path) => path.endsWith(`/${siteFileName}`))
  const defaultPath = allPaths.find((path) => path.endsWith(`/${defaultFileName}`))

  if (matchedPath) {
    modules[matchedPath]().then((module) => {
      component.value = module.default
    })
  } else if (defaultPath) {
    // 找不到环境组件，使用默认组件
    modules[defaultPath]().then((module) => {
      component.value = module.default
    })
  } else {
    console.error(`找不到匹配的组件: ${siteFileName} 和默认组件: ${defaultFileName}`)
  }

  return { component }
}
