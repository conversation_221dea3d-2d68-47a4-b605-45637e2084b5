<template>
  <view class="course h-full">
    <KyScrollViewV2
      v-model:current="current"
      :tabList="tabList"
      :apiFun="getDataList"
      :scrollCount="1"
      :active-style="{
        backgroundColor: 'var(--primary-color)',
        color: '#fff',
        padding: '9rpx 19rpx',
        borderRadius: '99rpx',
        lineHeight: '1',
      }"
    >
      <template #default="{ data }">
        <view class="list grid grid-cols-[1fr_1fr] px-20rpx gap-20rpx lg:grid-cols-4">
          <view
            v-for="item in data"
            class="item rounded-20rpx overflow-hidden h-340rpx"
            @click="clickCourse(item)"
          >
            <ky-image :src="item.cimage" width="100%" height="194rpx" rounded="20rpx 20rpx 0 0" />
            <view class="ellipsis px16rpx text-#333333 text-26rpx font-bold my-10rpx">
              {{ item.cname }}
            </view>
            <view class="px16rpx">
              <text v-if="item.topCategoryName" class="tag mr-10rpx">
                {{ item.topCategoryName }}
              </text>
              <text class="tag">{{ item.period }}学时</text>
            </view>
            <view class="sign" :style="{ background: getStudyColor(item.studyStatus) }">
              {{ STUDY_STATUS[item.studyStatus] }}
            </view>
          </view>
        </view>
      </template>
    </KyScrollViewV2>

    <ky-dialog v-model:visible="hintVisible" title="提示" @confirm="handleHintConfirm">
      <div class="text-#333">
        学习时间不允许重叠，一门课程学完后才能继续学习 下一门课程，现在是去学习上一个未完成的课程？
      </div>
    </ky-dialog>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { getStudyColor, STUDY_STATUS } from '@/utils/dataStatus'
import { toPage } from '@/utils/toPage'
import { httpGet, httpPost } from '@/utils/uniHttp'
import KyDialog from '@/components/KyDialog.vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const current = ref(0)
const tabList = ref(['全部', '未学习', '学习中', '已学完'])
const tabsValue = [[], ['01'], ['02'], ['03']]

const getDataList = async (params) => {
  return httpPost(
    '/study/portal/course/myCoursePage',
    { studyStatusList: tabsValue[current.value] },
    params,
  )
}

const hintVisible = ref(false)
let needStudyCourseId
const clickCourse = async (item) => {
  // 湖南农大事业单位需按照顺序学习
  if (import.meta.env.VITE_SITE_NAME === 'hnndsydw' && item.studyStatus !== '03') {
    const res = await httpGet('/study/portal/play/hasOtherXxz?courseId=' + item.courseId)
    if (res.code === 0) {
      // 还有未学完的课程
      needStudyCourseId = res.data
      hintVisible.value = true
      return
    }
  }
  toPage(`/pages/course/details`, { id: item.courseId, studyFlag: true })
}

const handleHintConfirm = () => {
  toPage(`/pages/course/details`, { id: needStudyCourseId, studyFlag: true })
  hintVisible.value = false
}
</script>

<style scoped lang="scss">
.course {
  height: 100%;

  :deep(.z-tabs-conatiner) {
    background-color: transparent !important;
    .z-tabs-bottom {
      display: none;
    }
    .z-tabs-item-title-rpx {
      font-size: 24rpx;
    }
  }

  .list {
    .item {
      position: relative;
      background-color: #fff;

      // .tag {
      //   padding: 2rpx 4rpx;
      //   font-size: 20rpx;
      //   font-weight: 400;
      //   color: #b6b6b6;
      //   border: 1px solid #cccccc;
      //   border-radius: 3px;
      // }

      .sign {
        position: absolute;
        right: 0;
        bottom: 0;
        padding: 10rpx 19rpx;
        font-size: 22rpx;
        color: #fff;
        border-radius: 20rpx 0 20rpx 0;
      }
    }
  }
}
</style>
