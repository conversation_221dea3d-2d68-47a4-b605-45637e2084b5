<template>
  <view class="w-full h-full rounded-10rpx overflow-hidden relative bg-red">
    <!--    <image-->
    <!--      :src="getCdnImageUrl('/course/rline.png')"-->
    <!--      mode="widthFix"-->
    <!--      class="!absolute !bottom-0 !left-0 w-110%"-->
    <!--    />-->
    <epii-camera ref="camera" :style="{width,height}"></epii-camera>
  </view>
</template>

<script>
import { getCdnImageUrl } from "@/utils/images";

export default {
  name: "App-Camera",
  props: {
    width: {
      type: String,
      default: "600rpx"
    },
    height: {
      type: String,
      default: "800rpx"
    }
  },
  data() {
    return {
      context: null
    };
  },
  mounted() {
    this.startPreview();
  },
  methods: {
    getCdnImageUrl,
    startPreview() {
      console.log("=>(App-Camera.nvue:43) this.$refs.camera", this.$refs.camera);
      //安卓解决拉伸问题，一般有两种思路。
      //1. 组件宽高不变，找出最适合的分辨比率
      //2. 根据摄像头的分辨率，来重新修改组件的宽和高。
      let type = 1;//1  组件宽高不变，找出最适合的分辨比率  2根据摄像头的分辨率，来重新修改组件的宽和高。
      this.$refs.camera.getSupportedPreviewSizesByCameraId({
        cameraId: 1
      }, (data) => {
        let bestHeight_index = 0;
        //方法1 组件宽高不变，找出最适合的分辨比率，这样只能减少拉伸，不能解决
        if (type === 1) {
          let r0 = (this.height * 1.0) / this.width;
          let diff = 1000;
          for (let i = 0; i < data.sizes.length; i++) {
            //找出最合适的分辨率，宽高比例尽量接近组件的（摄像头宽高是反着呢）
            let tmp = Math.abs(r0 - (data.sizes[i].width * 1.0) / data.sizes[bestHeight_index].width);
            if (tmp < diff) {
              diff = tmp;
              bestHeight_index = i;
            }
          }
          //方法2 先获取摄像头 支持的分辨率，然后修改组件的大小（一般保持宽不变），这样肯定不拉伸
        } else if (type === 2) {
          for (let i = 0; i < data.sizes.length; i++) {
            console.log(data.sizes[i]);
            //找出最大高度对应的分辨比率（宽高是反着呢）
            if (data.sizes[i].width > data.sizes[bestHeight_index].width) {
              bestHeight_index = i;
            }
          }
          //this.width = data.sizes[max_index].height;
          //计算出当前宽度下最适合的高度，然后修改组件的高
          this.height = ((data.sizes[bestHeight_index].width / (data.sizes[bestHeight_index].height * 1.0)) * 750) + "rpx";
          // 3 用最大分辨比率，不考虑拉伸
        } else if (type === 3) {
          bestHeight_index = 0;//最大的一般是第一个，你也可以遍历后找出最大分辨比率

        }
        //延迟打开摄像头，组件大小修改需要时间
        setTimeout(() => {
          this.$refs.camera.openCamera({
            cameraId: 1,
            previewWidth: data.sizes[bestHeight_index].width,
            previewHeight: data.sizes[bestHeight_index].height
          }, function(data) {
          });
        }, 500);
      });
    },
    takePhoto() {
      return new Promise((resolve) => {
        this.$refs.camera.takePicture({
          base64: false
        }, function(data) {
          resolve("file://" + data.img);
        });
      });
    },
    startRecord() {
      this.$refs.camera.startRecord({}, (ret) => {
        uni.showModal({
          content: JSON.stringify(ret)
        });
      });
    },
    stopRecord() {
      this.$refs.camera.stopRecord(function(data) {
        //可以通过 uni.compressVideo 压缩视频 记得前缀 file://
        uni.showModal({
          content: JSON.stringify(data)
        });
        uni.saveVideoToPhotosAlbum({
          filePath: "file://" + data.path,
          success: function() {
            console.log("save success");
          }
        });
      });
    },
    release() {
      this.$refs.camera.release();
    }
  }
};
</script>

<style></style>
