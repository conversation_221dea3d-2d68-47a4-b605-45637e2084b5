<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的发票',
  },
}
</route>

<template>
  <div class="container flex flex-col">
    <wd-tabs v-model="tab" :slidable-num="1">
      <wd-tab title="开票申请"></wd-tab>
      <wd-tab title="发票下载"></wd-tab>
    </wd-tabs>
    <div class="flex-1">
      <ApplyInvoice v-if="tab === 0" />
    </div>
  </div>
</template>

<script setup>
import ApplyInvoice from './components/ApplyInvoice.vue'

const tab = ref(0)
</script>

<style lang="scss" scoped></style>
