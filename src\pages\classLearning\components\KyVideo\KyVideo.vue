<template>
  <view>
    <!-- 视频 -->
    <!-- :controls="false" -->
    <!-- :show-progress="false" -->
    <!-- :show-progress="cw.studyStatus === '03'" -->
    <video
      v-if="videoConfig?.url"
      id="studyVideo"
      autoplay
      playsinline
      webkit-playsinline
      x5-video-player-type="h5"
      x5-video-player-fullscreen="false"
      x5-video-orientation="portraint"
      class="w-full"
      :class="class"
      :src="videoConfig.url"
      :show-fullscreen-btn="false"
      :enable-progress-gesture="false"
      :initial-time="videoConfig.initTime"
      :show-background-playback-button="false"
      :show-progress="cw.studyStatus === '03'"
      :muted="false"
      @pause="handlePause"
      @play="handlePlay"
      @timeupdate="handleTimeUpdate"
      @ended="handleEnded"
    ></video>
    <image v-else :src="poster" mode="scaleToFill" :class="class" />
    <template v-if="props.photograph">
      <!-- 拍照 -->
      <Photograph
        v-model:visible="photographVisible"
        :cw="cw"
        :type="photographType"
        @verify="handleVerify"
        @opened="handlePhotographOpend"
      />
      <!-- 抓拍 -->
      <CandidPhotograph
        v-if="candidPhotographVisible && !photographVisible"
        ref="candidPhotographComp"
        :cw="cw"
        @verify="handleCandidVerify"
      />
    </template>
    <TopicVerify
      v-if="topicVerify"
      v-model:visible="topicVerifyVisible"
      :cw="cw"
      @verify="handleTopicVerify"
      @opened="handlePhotographOpend"
    />
    <CodeVerify
      v-model:visible="codeVerifyVisible"
      :cw="cw"
      @verify="handleTopicVerify"
      @opened="handlePhotographOpend"
    ></CodeVerify>
  </view>
</template>

<script setup>
import { sleep } from '@/utils/sleep'
import { httpGet, httpPost } from '@/utils/uniHttp'
import { cloneDeep, throttle } from 'lodash-es'
import { useToast } from 'wot-design-uni'
import CandidPhotograph from '../Photograph/CandidPhotograph.vue'
import Photograph from '../Photograph/Photograph.vue'
import { checkPermission } from '@/utils/permission'
import { useVideoProgress } from '@/hooks/useVideoProgress'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { useRoute } from 'vue-router'
import {
  defineExpose,
  defineProps,
  defineEmits,
  onMounted,
  onUnmounted,
  ref,
  reactive,
  watch,
  nextTick,
  getCurrentInstance,
} from 'vue'
import TopicVerify from '../TopicVerify/index.vue'
import CodeVerify from '../CodeVerify/index.vue'
import qs from 'qs'
import { toPage } from '@/utils/toPage'
import { topicVerify_show } from '@/config'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  poster: {
    type: String,
    default: '',
  },
  courseList: {
    type: Array,
    default: () => [],
  },
  class: {
    type: String,
    default: '',
  },
  clearPlayInfo: {
    type: Function,
    default: () => {},
  },
  // 是否开启拍照
  photograph: {
    type: Boolean,
    default: true,
  },
  // 是否开启题目验证
  topicVerify: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['ended'])
const toast = useToast()
const instance = getCurrentInstance()
const siteName = import.meta.env.VITE_SITE_NAME

// 提交进度和检查拍照 videoContext.exitFullScreen();
const { run, clear, checkPhoto, submit } = useVideoProgress(
  {
    time: 30000,
    params: () => ({
      courseId: cw.value.courseId,
      coursewareId: cw.value.resId,
      lessonLocation: videoConfig.lessonLocation,
    }),
    handle: (res) => {
      console.log('submit：', res)
      if (res.code !== 0) {
        uni.showModal({
          title: '警告',
          showCancel: false,
          content: res.msg,
          complete: () => {
            // #ifdef H5
            window.location.reload()
            // #endif
          },
        })
        cw.value.playFlag = false
      }
    },
  },
  props.photograph || props.topicVerify
    ? {
        time: 13000,
        params: () => ({
          courseId: cw.value.courseId,
          coursewareId: cw.value.resId,
          lessonlocation: videoConfig.lessonLocation,
        }),
        handle: (res) => {
          console.log('getAction：', res)
          if (res.action === '2') {
            photographType = '1'
            photographVisible.value = true
          } else if (res.action === '4') {
            topicVerifyVisible.value = true
          } else if (res.action === '10') {
            codeVerifyVisible.value = true
          }
        },
      }
    : null,
)

let photographType = '1' // 1:拍照 2:录制视频
const videoConfig = reactive({
  url: '',
  initTime: 0,
  lessonLocation: 0,
})
const cw = ref({})
const videoContext = ref(null)
const photographVisible = ref(false) //拍照弹窗
// 播放视频
let url = ''
const play = async (_cw) => {
  return new Promise(async (resolve, reject) => {
    // 处理重复点击同一视频的情况
    if (cw.value.id === _cw.id && videoConfig.url) {
      console.log('重复点击同一视频')
      cw.value.playFlag = !cw.value.playFlag
      return resolve()
    }

    videoConfig.url = ''

    // 更新当前课件信息
    cw.value = _cw
    const { courseId, resId: coursewareId, studyStatus, lessonLocation } = cw.value
    // 获取视频配置信息
    const beginConfig = await useRequestWrap(() =>
      httpPost('/study/process/record/begin', {
        courseId,
        coursewareId,
        clientFlag: 'app',
      }),
    )
    const playConfig = await useRequestWrap(
      () =>
        httpGet('/study/portal/play/config', {
          courseId,
          coursewareId,
        }),
      (res) => {
        if (res.statusCode === 500) reject(res)
      },
    )
    console.log('beginConfig', beginConfig)

    // 设置视频上下文和配置
    captureConfig = beginConfig.captureConfig
    videoContext.value = uni.createVideoContext('studyVideo', instance)
    url = playConfig.url
    videoConfig.initTime = studyStatus === '03' ? 0 : lessonLocation
    videoConfig.lessonLocation = lessonLocation
    setTimeout(() => {
      videoContext.value.seek(videoConfig.initTime)
    }, 300)

    // 非首次学习需要进行拍照检查、开启抓拍摄像头
    if (studyStatus !== '03' && (props.photograph || props.topicVerify)) {
      const { action } = await checkPhoto()
      if (action === '2') {
        photographType = '1'
        photographVisible.value = true
        return resolve()
      } else if (action === '4') {
        topicVerifyVisible.value = true
        return resolve()
      } else if (action === '10') {
        codeVerifyVisible.value = true
        return resolve()
      } else {
        // candidPhotographVisible.value = true
      }
    }

    videoConfig.url = url
    cw.value.playFlag = true
    console.log(' cw-->', cw)
    resolve()
  })
}
// 拍照弹框打开回调
const handlePhotographOpend = () => {
  // setTimeout(() => {
  console.log('拍照弹框打开的暂停')
  cw.value.playFlag = false
  clear()
  // }, 300)
}
// 拍照结果
const handleVerify = (res, { id } = {}) => {
  console.log('拍照结果 -->', res, id)
  if (!res) {
    cw.value.playFlag = false
    props.clearPlayInfo()
    return
  }
  videoConfig.url = url
  nextTick(() => {
    setTimeout(() => {
      cw.value.playFlag = true
    }, 200)
  })
  run()
  if (photographType === '2') {
    captureConfig.videoId = id
  }
}

// 抓拍
let captureConfig = null //抓拍配置
const candidPhotographComp = ref(null)
const candidPhotographVisible = ref(false) //抓拍弹窗
const handleCandidPhotograph = throttle(async (time) => {
  if (!cw.value.playFlag) return
  if (!captureConfig) return
  console.log('抓拍配置：', time, captureConfig)
  const { imageLocation, imageId, videoLocation, videoId } = captureConfig
  if (!imageId && time > imageLocation) {
    candidPhotographVisible.value = true
    setTimeout(() => {
      // 照片抓拍
      candidPhotographComp.value.takePhoto()
    }, 5000)
  } else if (!videoId && time > videoLocation) {
    // 视频抓拍
    photographType = '2'
    photographVisible.value = true
  }
}, 10000)
// 抓拍结果
const handleCandidVerify = (res, { id } = {}) => {
  candidPhotographVisible.value = false
  if (!res) {
    cw.value.playFlag = false
    return
  }
  captureConfig.imageId = id
  cw.value.playFlag = true
}

// 题目校验
const topicVerifyVisible = ref(false) //题目验证弹窗
const handleTopicVerify = () => {
  videoConfig.url = url
  cw.value.playFlag = true
}
let stageId
const topicPlayEffect = async () => {
  if (topicVerify_show.includes(siteName)) {
    const params = {
      lessonLocation: cw.value.lessonLocation,
      courseId: cw.value.courseId,
      coursewareId: cw.value.resId,
    }
    const res = await useRequestWrap(() =>
      httpGet(`/study/portal/play/start?${qs.stringify(params)}`),
    )
    stageId = res
  }
}
const topicPauseEffect = async () => {
  if (topicVerify_show.includes(siteName)) {
    if (!stageId) return
    const params = {
      lessonLocation: cw.value.lessonLocation,
      stageId,
      pushFlag: topicVerifyVisible.value ? '1' : '0',
    }
    const res = await useRequestWrap(() =>
      httpGet(`/study/portal/play/end?${qs.stringify(params)}`),
    )
  }
}

// 验证码校验
const codeVerifyVisible = ref(false)

const isPlay = ref(false)
// 暂停
const handlePause = () => {
  clear()
  isPlay.value = false
  cw.value.playFlag = false
  candidPhotographVisible.value = false

  topicPauseEffect()
}
// 播放
const handlePlay = async () => {
  clear()
  isPlay.value = true
  cw.value.playFlag = true

  // 重置倍速检测变量
  lastCheckTime = Date.now()
  lastVideoTime = 0

  if (cw.value.studyStatus === '03') return
  // candidPhotographVisible.value = true

  if (!props.photograph && !props.topicVerify) {
    run()
    return
  }

  const { action } = await checkPhoto()
  if (action === '2') {
    photographType = '1'
    photographVisible.value = true
    return
  } else if (action === '4') {
    topicVerifyVisible.value = true
    return
  } else if (action === '10') {
    codeVerifyVisible.value = true
    return
  }

  topicPlayEffect()

  run()
}
// 添加倍速检测相关变量
let lastCheckTime = Date.now()
let lastVideoTime = 0

// 时间更新
const handleTimeUpdate = throttle(async (e) => {
  if (!videoConfig.url || cw.value.studyStatus === '03' || !cw.value.playFlag) return

  const time = Number(e.detail.currentTime) // 使用Number保留精度
  // const currentRealTime = Date.now()

  // // 倍速检测
  // if (lastVideoTime > 0) {
  //   const videoTimeDiff = time - lastVideoTime
  //   const realTimeDiff = (currentRealTime - lastCheckTime) / 1000

  //   // 添加缓冲检测
  //   const isBuffering = realTimeDiff > 2 // 如果两次检测间隔超过2秒，可能是在缓冲

  //   console.log('视频倍率', videoTimeDiff / realTimeDiff)
  //   if (videoTimeDiff > 0 && realTimeDiff > 0 && !isBuffering && videoTimeDiff / realTimeDiff > 5) {
  //     const res = await useRequestWrap(() =>
  //       httpGet(
  //         `/study/portal/play/user/courseware?courseId=${cw.value.courseId}&coursewareId=${cw.value.resId}`,
  //       ),
  //     )

  //     // 修改进度
  //     videoContext.value.seek(res.lessonLocation)
  //     cw.value.lessonLocation = res.lessonLocation
  //     cw.value.playFlag = false

  //     uni.showModal({
  //       title: '警告',
  //       showCancel: false,
  //       content:
  //         '不要倍速播放或拖动进度条，否则学时进度记录无效！倍率：' +
  //         (videoTimeDiff / realTimeDiff).toFixed(2),
  //       complete: () => {
  //         // 使用complete替代success，确保一定会执行
  //         // 重置检测变量
  //         lastCheckTime = Date.now()
  //         lastVideoTime = time
  //       },
  //     })
  //     return
  //   }
  // }

  // // 正常播放时更新检测变量
  // if (time > lastVideoTime) {
  //   // 只在正向播放时更新
  //   lastCheckTime = currentRealTime
  //   lastVideoTime = time
  // }

  // 更新课件进度
  videoConfig.lessonLocation = parseInt(time)
  time && (cw.value.lessonLocation = parseInt(time))

  if (!props.photograph) return
  handleCandidPhotograph(parseInt(time))
}, 1000)
// 视频结束
const handleEnded = async () => {
  if (cw.value.studyStatus === '03') {
    emit('ended')
    return
  }
  videoConfig.lessonLocation = cw.value.period
  await submit()

  const res = await useRequestWrap(() =>
    httpGet(
      `/study/portal/play/user/courseware?courseId=${cw.value.courseId}&coursewareId=${cw.value.resId}`,
    ),
  )
  if (res.studyStatus !== '03') {
    cw.value.lessonLocation = res.lessonLocation
    cw.value.playFlag = true
  } else {
    cw.value.lessonLocation = cw.value.period
    cw.value.studyStatus = '03'
    emit('ended')
  }
}

// 播放暂停
let timer
watch(
  () => cw.value?.playFlag,
  (newVal) => {
    if (newVal) {
      console.log('开始播放')
      videoContext.value?.play()
      clearInterval(timer)
      timer = setInterval(() => {
        console.log(':播放', videoContext)

        if (isPlay.value) {
          clearInterval(timer)
          return
        }
        videoContext.value?.play()
      }, 100)
    } else {
      console.log('暂停播放')
      videoContext.value?.pause()
    }
  },
)
onMounted(async () => {
  if (props.photograph) {
    // #ifndef APP-PLUS
    const res = await checkPermission(['camera', 'record'])
    console.log('res -->', res)
    // if (!res.success) {
    //   uni.navigateBack()
    // }
    // #endif
  }
})
onUnmounted(() => {
  clear()
})

defineExpose({
  play,
  cw,
})
</script>

<style lang="scss" scoped>
.video-height {
  height: calc(100vw * 9 / 16);
}
</style>
