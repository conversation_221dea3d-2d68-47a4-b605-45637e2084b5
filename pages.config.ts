import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  pages: [
    {
      path: 'sydw/login',
      style: {
        navigationBarTitleText: '登录',
        navigationStyle: 'custom',
      },
    },
    {
      path: 'p/course/uncompleted',
      style: {
        navigationBarTitleText: '支付',
        navigationStyle: 'custom',
      },
    },
    {
      path: 'bitMatrix/imageprint',
      style: {
        navigationBarTitleText: '证书',
      },
    },
  ],
  globalStyle: {
    navigationStyle: 'default',
    // navigationBarTitleText: '广东学习网',
    navigationBarBackgroundColor: '#FFFFFF',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
      '^u--(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^up-(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^u-([^-].*)': 'uview-plus/components/u-$1/u-$1.vue',
    },
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#FF6700',
    backgroundColor: '#fff',
    borderStyle: 'black',
    height: '0px',
    fontSize: '10px',
    spacing: '4px',
    iconWidth: '24px',
    list: [
      {
        iconPath: 'static/tabbar/index_n.png',
        selectedIconPath: 'static/tabbar/index_s.png',
        pagePath: 'pages/home/<USER>',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/course_n.png',
        selectedIconPath: 'static/tabbar/course_s.png',
        pagePath: 'pages/course/course',
        text: '课程',
      },
      {
        iconPath: 'static/tabbar/xx_n.png',
        selectedIconPath: 'static/tabbar/xx_s.png',
        pagePath: 'pages/study/study',
        text: '学习',
      },
      {
        iconPath: 'static/tabbar/me_n.png',
        selectedIconPath: 'static/tabbar/me_s.png',
        pagePath: 'pages/my/my',
        text: '我',
      },
    ],
  },
})
