<template>
  <!-- #ifndef MP-WEIXIN -->
  <component :is="component" />
  <!-- #endif -->
  <!-- #ifdef MP-WEIXIN -->
  <defaultComponent />
  <!-- #endif -->
</template>

<script setup>
import { useSiteComponent } from '@/hooks/useSiteComponent'
import defaultComponent from './default.vue'

let modules
let component

const type = uni.getSystemInfoSync().uniPlatform
if (type === 'web') {
  // 在组件中获取当前目录的所有Vue文件
  modules = import.meta.glob('./*.vue')
  const siteComponent = useSiteComponent(modules)
  component = siteComponent.component
}
</script>
