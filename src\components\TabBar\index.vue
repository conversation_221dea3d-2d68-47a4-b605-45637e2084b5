<template>
  <!-- #ifndef MP-WEIXIN -->
  <div class="h-56px">
    <div
      class="h-56px fixed bottom-0 left-0 right-0 flex items-center justify-around bg-#fff z-1000"
    >
      <div v-for="item in list" class="flex flex-col items-center" @click="to(item.path)">
        <SvgIcon
          :icon="route.path === item.path ? item.icon + '_active' : item.icon"
          width="18px"
          height="18px"
        />
        <div
          class="text-13px text-#B6B6B6 mt-3px"
          :class="{ '!text-[var(--primary-color)]': route.path === item.path }"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
  </div>
  <!-- #endif -->
</template>

<script setup>
import { useRoute } from 'vue-router'
import SvgIcon from '@/components/SvgIcon.vue'

const route = useRoute()

const list = [
  {
    icon: 'home',
    text: '首页',
    path: '/',
  },
  {
    icon: 'course',
    text: '课程',
    path: '/pages/course/course',
  },
  {
    icon: 'study',
    text: '学习',
    path: '/pages/study/study',
  },
  {
    icon: 'me',
    text: '我',
    path: '/pages/my/my',
  },
]

const to = (path) => {
  uni.switchTab({
    url: path,
  })
}
</script>

<style lang="scss" scoped></style>
