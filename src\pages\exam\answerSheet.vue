<route lang="json5">
{
  style: {
    navigationBarTitleText: '答题卡',
  },
}
</route>
<template>
  <view class="container w-80% mx-auto my-20px">
    <view v-for="(item, key, index) in postAnswerList" :key="key" class="mb-40rpx">
      <view class="text-14px text-#999 mb-20rpx">{{ QUESTION_TYPE[key] }}题</view>
      <view class="flex gap10rpx">
        <view
          v-for="(cell, cellIndex) in item"
          :key="item.id"
          class="w-80rpx h-60rpx flex justify-center items-center rounded-10rpx"
          :class="cell.userAnswer && ['bg-[var(--primary-color)]', 'text-white']"
        >
          {{ cell.index + 1 }}
        </view>
      </view>
    </view>
    <wd-button class="w-full mt-20rpx" @click="handleSubmit" :round="false">
      交卷并查看结果
    </wd-button>
  </view>

  <KyDialog v-model:visible="notFinishVisible" @confirm="submit">
    <view class="text-30rpx text-#333 py-20rpx">还有题目未作答，是否确定交卷</view>
  </KyDialog>

  <KyDialog
    v-model:visible="examResultVisible"
    @confirm="resultConfirm"
    title="考试结果"
    :show-cancel-button="false"
  >
    <view class="text-30rpx text-#333 pb-20rpx">
      <text>考试得了{{ examResult.userScore }}分</text>
      <text v-if="examResult.pass">恭喜你，通过了考试</text>
      <text v-else>未通过此次考试</text>
    </view>
  </KyDialog>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import { useExamStore } from '@/store'
import { QUESTION_TYPE } from '@/utils/dataStatus'
import { toPage } from '@/utils/toPage'
import { httpPost } from '@/utils/uniHttp'

const { answerList } = useExamStore()
const { showLoading, hideLoading } = useLoading()
let classType = ''
let isFinish = true
let courseId = ''
const postAnswerList = computed(() => {
  const map = {}
  answerList.forEach((item, index) => {
    if (!map[item.type]) map[item.type] = []
    if (!item.userAnswer) isFinish = false
    map[item.type].push({ ...item, index })
  })
  return map
})
const notFinishVisible = ref(false)
const handleSubmit = () => {
  if (!isFinish) {
    notFinishVisible.value = true
  } else {
    submit()
  }
}

const examResult = ref({})
const submit = async () => {
  notFinishVisible.value = false
  showLoading('提交中...')
  const res = await useRequestWrap(
    () => {
      if (classType === 'ejClass') {
        return httpPost('/study/portal/exam/planApplyExam', {
          questionList: answerList,
          planId: courseId,
        })
      } else {
        return httpPost('/study/portal/exam/applyExam', { questionList: answerList, courseId })
      }
    },
    () => {
      hideLoading()
    },
  )
  examResult.value = res
  examResultVisible.value = true
}
const examResultVisible = ref(false)
const resultConfirm = () => {
  examResultVisible.value = false
  if (classType === 'ejClass') {
    uni.navigateBack({
      delta: 2,
    })
  } else {
    uni.switchTab({ url: '/pages/study/study' })
  }
}

onLoad((options) => {
  console.log('options -->', options)
  courseId = options.courseId
  classType = options.classType
  if (options.flag === 'true') {
    submit()
  }
})
</script>

<style lang="scss" scoped></style>
