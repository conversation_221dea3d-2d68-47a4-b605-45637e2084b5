import './style/base.scss'
import './style/wot-ui.scss'
import './style/uview.scss'
import { createSSRApp } from 'vue'
import App from './App.vue'
import store, { Pinia } from './store'
import { routeInterceptor, requestInterceptor, prototypeInterceptor } from './interceptors'
import 'virtual:uno.css'
import uviewPlus from 'uview-plus'
import 'virtual:svg-icons-register'

const isDev = import.meta.env.DEV

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(uviewPlus)
  // 开发环境开启vconsole
  // #ifdef H5
  if (isDev) {
    import('vconsole').then((VConsoleModule) => {
      const VConsole = VConsoleModule.default
      const vConsole = new VConsole()
      app.use(vConsole)
    })
  }
  // #endif
  return {
    app,
    Pinia,
  }
}
