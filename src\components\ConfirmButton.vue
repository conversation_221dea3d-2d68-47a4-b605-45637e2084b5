<template>
  <view>
    <wd-button :size="size" :plain="plain" :type="type" :class="customClass" @click="handleClick">
      <slot />
    </wd-button>
    <wd-message-box></wd-message-box>
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'

defineOptions({
  inheritAttrs: false,
})
const props = withDefaults(
  defineProps<{
    msg: string
    title: string
    size?: string
    plain?: boolean
    type?: string
    customClass?: string
  }>(),
  {
    title: '提示',
    msg: '确认删除吗？',
  },
)
const emits = defineEmits(['confirm', 'cancel'])
const message = useMessage()

const handleClick = () => {
  message
    .confirm({
      title: props.title,
      msg: props.msg,
    })
    .then(() => {
      emits('confirm')
    })
    .catch(() => {
      emits('cancel')
    })
}
</script>

<style lang="scss" scoped></style>
