type PermissionType = 'camera' | 'album' | 'record' | 'userLocation' | 'userInfo'

interface CheckResult {
  success: boolean
  error?: any
}

const permissionTextMap = {
  camera: '相机',
  album: '相册',
  record: '录音',
  userLocation: '位置',
  userInfo: '用户信息',
} as const

/**
 * 通用权限检查函数
 * @param authTypes 权限类型或权限类型数组
 * @returns Promise<CheckResult>
 */
export async function checkPermission(
  authTypes: PermissionType | PermissionType[],
): Promise<CheckResult> {
  const authList = Array.isArray(authTypes) ? authTypes : [authTypes]

  try {
    // #ifndef H5
    // 小程序和APP的处理逻辑
    const result = await new Promise((resolve, reject) => {
      uni.getSetting({
        success: async (res) => {
          const tasks = authList.map((type) => {
            return new Promise((resolve, reject) => {
              const scope = `scope.${type}`
              if (!res.authSetting[scope]) {
                uni.authorize({
                  scope,
                  success: () => resolve(true),
                  fail: (err) => {
                    uni.showModal({
                      title: '提示',
                      content: `是否前往设置页面开启${permissionTextMap[type]}权限？`,
                      success: (modalRes) => {
                        if (modalRes.confirm) {
                          uni.openSetting({
                            success: (settingRes) => {
                              settingRes.authSetting[scope] ? resolve(true) : reject(err)
                            },
                          })
                        } else {
                          reject(err)
                        }
                      },
                    })
                  },
                })
              } else {
                resolve(true)
              }
            })
          })

          try {
            await Promise.all(tasks)
            resolve(true)
          } catch (err) {
            reject(err)
          }
        },
        fail: reject,
      })
    })
    // #endif

    // #ifdef H5
    // H5的处理逻辑
    for (const type of authList) {
      switch (type) {
        case 'camera':
        case 'record':
          const constraints: MediaStreamConstraints = {
            video: type === 'camera',
            audio: type === 'record',
          }
          const stream = await navigator.mediaDevices.getUserMedia(constraints)
          stream.getTracks().forEach((track) => track.stop())
          break

        case 'userLocation':
          await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject)
          })
          break

        case 'album':
          // H5无法直接检查相册权限，这里直接返回成功
          break

        case 'userInfo':
          // H5一般不需要特别的用户信息权限
          break

        default:
          throw new Error(`未知的权限类型: ${type}`)
      }
    }
    // #endif
    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error,
    }
  }
}
