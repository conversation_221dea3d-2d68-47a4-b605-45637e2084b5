pipeline {
    agent {
       node {
           label 'node18'
       }
    }

    environment {
        APP_NAMESPACE = 'cloud-edu-project'
        APP_NAME = 'gdhbjyw-h5'
        KUBECONFIG_CREDENTIAL_ID = 'simple-kubeconfig'
        DOCKER_CREDENTIAL_ID = 'docker-id'
        REGISTRY = 'harbor.test-whky.com'
        DOCKERHUB_NAMESPACE = 'cloud-edu'
        PORT = '80'
        REPLICAS = 1
    }

    stages {
        stage ('checkout scm') {
            steps {
                checkout(scm)
            }
        }


        stage ('build & push') {
            steps {
                container ('nodejs') {
                    // 设置 npm 配置
                    sh '''
                        npm config set registry https://registry.npmmirror.com
                        npm config set strict-ssl false
                        npm config set proxy null
                        npm config set https-proxy null
                        npm install -g pnpm
                    '''

                    // 设置 pnpm 配置
                    sh '''
                        pnpm config set registry https://registry.npmmirror.com
                        pnpm store prune
                        pnpm install
                        pnpm run build:gdhbjyw
                    '''
                    sh 'docker build -f deploy/Dockerfile-dev -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:$BUILD_NUMBER --build-arg TRAILING_SLASH=false .'
                    withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                        sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                        sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:$BUILD_NUMBER'
                    }
                }
            }
        }

        stage('push latest'){
           steps{
                container ('nodejs') {
                  sh 'docker tag  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:$BUILD_NUMBER $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:latest'
                  sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:latest'
                }
           }
        }

        stage('deploy') {
          steps {
            container ('nodejs') {
                  withCredentials([
                      kubeconfigFile(
                      credentialsId: env.KUBECONFIG_CREDENTIAL_ID,
                      variable: 'KUBECONFIG')
                      ]) {
                      sh 'envsubst < deploy/devops-k8s.yml | kubectl apply -f -'
                  }
            }
          }
        }
    }
}
