<route lang="json5">
{
  style: {
    navigationBarTitleText: '购物车',
  },
}
</route>
<template>
  <theme-wrap>
    <view class="container grid grid-rows-[1fr_auto]">
      <view v-if="cartList.length > 0" class="list overflow-auto">
        <wd-swipe-action v-for="item in cartList" :key="item.id">
          <view class="item flex items-center my20rpx h-180rpx bg-#fff p-20rpx">
            <wd-checkbox
              v-model="item.checked"
              :checked-color="themeStore.primaryColor"
              shape="square"
              size="large"
            ></wd-checkbox>
            <ky-image
              :src="item.cimage"
              width="300rpx"
              height="100%"
              rounded="20rpx"
              class="flex-shrink-0"
              @click="item.checked = !item.checked"
            ></ky-image>
            <view
              class="flex flex-col justify-between h-full flex-1 ml-20rpx e"
              @click="click_course(item)"
            >
              <view class="ellipsis-3">{{ item.cname }}</view>
              <view class="flex justify-between items-center">
                <view class="text-22rpx text-#999">
                  <text>{{ item.topCategoryName }}</text>
                  <text class="mx-10rpx">|</text>
                  <text>{{ item.period }}学时</text>
                </view>
                <view class="text-[var(--primary-color)]">￥{{ item.price }}</view>
              </view>
            </view>
          </view>
          <template #right>
            <wd-button
              class="!h-100% !w-180rpx !rounded-0 !bg-#c00 !text-30rpx !font-bold"
              :round="false"
              @click="handleDelete(item)"
            >
              删除
            </wd-button>
          </template>
        </wd-swipe-action>
      </view>
      <wd-status-tip v-else image="content" tip="暂无内容" />

      <!-- 底部结算 -->
      <view
        class="bg-#f4f7fa flex items-center justify-between shadow-[0_-2px_10px_rgba(0,0,0,0.1)] pl20rpx"
      >
        <view class="flex items-center gap20rpx">
          <wd-checkbox
            v-model="checkedAll"
            :checked-color="themeStore.primaryColor"
            shape="square"
            size="large"
            @change="handleCheckedAll"
          >
            全选
          </wd-checkbox>
          <view>合计：￥{{ totalPrice }}</view>
          <view v-if="periodStatistics_show.includes(siteName)">共{{ totalPeriod }}学时</view>
        </view>
        <wd-button
          :round="false"
          class="!h-58px !m-0 !rounded-0"
          :disabled="selectedCourse.length === 0"
          @click="handleGoPay"
        >
          去结算（{{ selectedCourse.length }}）
        </wd-button>
      </view>

      <wd-toast />
      <SelectPayType v-model:visible="selectPayTypeVisible" @confirm="handleSelectTypeConfirm" />
    </view>
  </theme-wrap>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import SelectPayType from '@/components/SelectPayType.vue'
import ThemeWrap from '@/components/ThemeWrap.vue'
import { periodStatistics_show, selectPayType } from '@/config'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { useThemeStore } from '@/store/theme'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

const themeStore = useThemeStore()
const { showLoading, hideLoading } = useLoading()
const siteName = import.meta.env.VITE_SITE_NAME

const cartList = ref([])

// 点击课程
const click_course = (item) => {
  uni.navigateTo({
    url: `/pages/course/details?id=${item.courseId}`,
  })
}
// 删除
const handleDelete = async (item) => {
  showLoading('删除中...')
  await useRequestWrap(
    () => httpGet('/study/portal/user/shop/car/removeCourse', { courseId: item.courseId }),
    () => hideLoading(),
  )
  getCartList()
}

// 底部
// 全选
const checkedAll = ref(false)
const handleCheckedAll = () => {
  cartList.value.forEach((item) => {
    item.checked = checkedAll.value
  })
}
// 合计
const totalPrice = computed(() => {
  return cartList.value.reduce((total, item) => {
    return item.checked ? total + item.price : total
  }, 0)
})
// 合计学时
const totalPeriod = computed(() => {
  return cartList.value.reduce((total, item) => {
    return item.checked ? total + item.period : total
  }, 0)
})
// 选择的课程
const selectedCourse = computed(() => {
  const arr = cartList.value.filter((item) => item.checked)
  if (arr.length < cartList.value.length || arr.length === 0) {
    checkedAll.value = false
  } else {
    checkedAll.value = true
  }
  return arr
})

// 去结算
const selectPayTypeVisible = ref(false)
const handleGoPay = async () => {
  if (selectPayType.includes(siteName)) {
    selectPayTypeVisible.value = true
    return
  }
  toPay()
}
const toPay = async (planType) => {
  const courseIds = selectedCourse.value.map((item) => item.courseId).join(',')
  const params = {
    courseIds,
  }
  if (planType) {
    params.planType = planType
  }
  const id = await useRequestWrap(() => httpGet('/study/portal/order/saveOrder', params))
  getCartList()
  toPage('/pages/payOrder/payOrder', { id })
}
const handleSelectTypeConfirm = (data) => {
  selectPayTypeVisible.value = false
  toPay(data)
}

const getCartList = async () => {
  const res = await useRequestWrap(() => httpGet('/study/portal/user/shop/car/selectShopCart'))
  cartList.value = res.map((item) => ({
    ...item,
    checked: true,
  }))
}
getCartList()
console.log('购物车')
</script>

<style lang="scss" scoped>
:deep(.wd-swipe-action__right) {
  right: -1px;
}
</style>
