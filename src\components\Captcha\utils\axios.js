/*
 * @Author: chenshengle <EMAIL>
 * @Date: 2023-09-25 09:20:35
 * @LastEditors: chenshengle <EMAIL>
 * @LastEditTime: 2024-05-06 14:58:08
 * @FilePath: \vue3\src\components\verifition\utils\axios.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from 'axios'

axios.defaults.baseURL = import.meta.env.VITE_BASE_API

const service = axios.create({
  timeout: 40000,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json; charset=UTF-8',
    'Tenant-Code': import.meta.env.VITE_SITE_CODE,
  },
})
service.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    Promise.reject(error)
  },
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data
    return res
  },
  (error) => {},
)
export default service
