import { CustomRequestOptions } from '@/interceptors/uniRequest'
import { useUserStore } from '@/store'
import { buildQueryString } from './toPage'

// 客户端类型
let clientType = 1
// #ifdef MP-WEIXIN
clientType = 1
// #endif
// #ifdef H5
clientType = 2
// #endif

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          resolve(res.data as IResData<T>)
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          uni.navigateTo({ url: '/pages/login/index' })
          // refreshToken()
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: (res.data as IResData<T>).msg || '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        // wx.showModal({
        //   title: '提示',
        //   content: JSON.stringify(err),
        // })
        uni.$emit('z-paging-error-emit') // 触发z-paging的错误事件
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export const httpGet = <T>(url: string, query?: Record<string, any>) => {
  query = {
    ...query,
    clientType,
  }
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
) => {
  query = {
    ...query,
    clientType,
  }
  console.log(' Postquery-->', query)
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
  })
}

/**
 * 文件上传请求
 * @param url 后台地址
 * @param filePath 文件路径
 * @param formData 额外的表单数据
 * @returns
 */
export const httpUpload = <T>(url: string, filePath: string, formData?: Record<string, any>) => {
  formData = {
    ...formData,
    clientType,
  }
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.uploadFile({
      url: url,
      filePath,
      name: 'file',
      formData,
      header: {
        Authorization: `Bearer ${useUserStore().authToken.accessToken}`,
      },
      success(res) {
        // uploadFile 的响应是字符串，需要手动转换为对象
        const data = JSON.parse(res.data) as IResData<T>
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(data)
        } else if (res.statusCode === 401) {
          refreshToken()
          reject(res)
        } else {
          uni.showToast({
            icon: 'none',
            title: data.msg || '上传失败',
          })
          reject(res)
        }
      },
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，请稍后重试',
        })
        reject(err)
      },
    })
  })
}

// 刷新token
function refreshToken() {
  const userStore = useUserStore()
  const { accessToken, refreshToken } = userStore.authToken
  // alert("token过期了")
  userStore.clear()
  // 获取当前路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // 获取查询参数
  const query = currentPage.options

  const redirectUrl = buildQueryString(query)
    ? `/${currentPage.route}?${buildQueryString(query)}`
    : `/${currentPage.route}`
  uni.setStorageSync('redirectUrl', redirectUrl)
  uni.redirectTo({ url: '/pages/login/index' })
}

http.get = httpGet
http.post = httpPost
http.upload = httpUpload
