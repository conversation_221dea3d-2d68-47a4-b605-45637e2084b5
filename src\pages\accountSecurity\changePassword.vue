<route lang="json5">
{
  style: {
    navigationBarTitleText: '修改密码',
  },
}
</route>
<template>
  <view class="container px-50rpx pt-60rpx bg-#fff">
    <view class="form">
      <!-- 设置新密码 -->
      <view>
        <view class="label !text-#333333">原密码</view>
        <PasswordInput v-model:password="oldPassword" placeholder="请输入原密码" />
      </view>
      <view>
        <view class="label !text-#333333">新密码</view>
        <PasswordInput v-model:password="newPassword" placeholder="请输入新密码" />
      </view>
      <view>
        <view class="label !text-#333333">确认新密码</view>
        <PasswordInput v-model:password="confirmPassword" placeholder="请再次输入新密码" />
      </view>
    </view>

    <wd-button
      class="mt60rpx !text-32rpx tracking-[20rpx] !w-full !h96rpx"
      :style="{
        boxShadow: `0 0 15px 0 ${getThemeColorWithOpacity()}`,
      }"
      :round="false"
      :disabled="!oldPassword || !newPassword || !confirmPassword"
      @click="handleSave"
    >
      保存
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import { httpPost } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'
import PasswordInput from '@/components/PasswordInput.vue'
import { getThemeColorWithOpacity } from '@/utils/color'
const toast = useToast()

const oldPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

const handleSave = async () => {
  if (newPassword.value !== confirmPassword.value) {
    toast.error('两次输入的新密码不一致')
    return
  }

  const res = await useRequestWrap(() =>
    httpPost('/admin-svc/user/changePassword', {
      oldPassword: oldPassword.value,
      newPassword: newPassword.value,
    }),
  )
  toast.success('修改密码成功')
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}
</script>

<style lang="scss" scoped>
:deep(.form) {
  display: flex;
  flex-direction: column;
  gap: 44rpx;
  .label {
    display: flex;
    gap: 6rpx;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    line-height: 20rpx;
    color: #666666;
  }
  .wd-input {
    padding-bottom: 10rpx;
    background: transparent;
    &::after {
      background: #eeeeee;
    }

    .wd-input__icon {
      background: transparent;
    }
    input {
      font-size: 32rpx;
    }
    .uni-input-placeholder {
      font-size: 32rpx !important;
      color: #c7c7c7;
    }
  }

  .is-not-empty {
    &::after {
      background: #eeeeee !important;
    }
  }
}

.disabled {
  background: #dedddd !important;
  box-shadow: none !important;
}
</style>
