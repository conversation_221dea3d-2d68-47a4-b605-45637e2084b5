<template>
  <div class="h-full">
    <div class="grid grid-cols-2 items-center bg-#fff">
      <wd-select-picker
        label="订单状态"
        v-model="queryData.applyInvoiceStatus"
        :columns="columns"
        type="radio"
      ></wd-select-picker>
      <wd-input v-model="queryData.ordno" placeholder="订单编号"></wd-input>
      <div class="px-16px col-span-2 box-border">
        <wd-button class="w-full !rounded-0">查询</wd-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'

const columns = ref([
  { value: '0', label: '未申请' },
  { value: '1', label: '待开票' },
  { value: '2', label: '已开票' },
])

const queryData = ref({
  applyInvoiceStatus: '',
  ordno: '',
})

const getApplyOrderList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/invoice/apply/order/list', { ...queryData }),
  )
  console.log(' res -->', res)
}
getApplyOrderList()
</script>

<style lang="scss" scoped>
:deep(.wd-select-picker__arrow) {
  display: none;
}
</style>
