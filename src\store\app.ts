import { httpGet } from '@/utils/uniHttp'
import { defineStore } from 'pinia'
import { useThemeStore } from './theme'
import { hexToRgba } from '@/utils/color'

export const useAppStore = defineStore(
  'app',
  () => {
    const baseConfig = ref<any>({})
    const userMenu = ref<any>([])

    // 更新favicon的函数
    const updateFavicon = (faviconUrl: string) => {
      let link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
      if (!link) {
        link = document.createElement('link') as HTMLLinkElement
        link.rel = 'icon'
        document.head.appendChild(link)
      }
      // 使用配置的favicon或默认的favicon.png
      link.href = faviconUrl || '/favicon.png'
    }
    // 获取基础配置
    const getBaseConfig = async () => {
      const res = await httpGet('/admin/portal/config/baseConfigInfo')
      baseConfig.value = res.data
      console.log('基础配置-->', res.data)
      const ThemeStore = useThemeStore()
      if (baseConfig.value.viewThemeColor) {
        ThemeStore.theme['--primary-color'] = baseConfig.value.viewThemeColor
        ThemeStore.theme['--parmary-color-ghost'] = hexToRgba(baseConfig.value.viewThemeColor, 0.2)
      } else {
        ThemeStore.theme['--parmary-color-ghost'] = hexToRgba(
          ThemeStore.theme['--primary-color'],
          0.2,
        )
      }
      // #ifndef MP-WEIXIN
      // 使用配置的viewFaviconIco或默认favicon
      updateFavicon(baseConfig.value.viewFaviconIco)
      // #endif
    }
    // 获取用户菜单
    const getUserMenu = async () => {
      const res = await useRequestWrap(() => httpGet('/admin/portal/config/left/bar/configs'))
      console.log(' res-->', res)
      // const res = await appApi.getUserMenu()
      userMenu.value = res
      // console.log('用户菜单-->', res)
    }

    const init = () => {
      getBaseConfig()
    }

    return {
      baseConfig,
      userMenu,
      getUserMenu,
      init,
    }
  },
  { persist: true },
)
