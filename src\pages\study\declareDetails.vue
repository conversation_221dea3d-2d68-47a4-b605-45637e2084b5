<route lang="json5">
{
  style: {
    navigationBarTitleText: '学时证明信息',
  },
}
</route>
<template>
  <view class="container">
    <view class="px-20rpx py-30rpx">
      <view class="w-full item rounded-20rpx overflow-hidden h-399rpx overflow-hidden relative">
        <ky-image :src="details.cimage" width="100%" height="100%" rounded="20rpx 0 0 20rpx" />
        <view
          class="absolute bottom-0 left-0 w-full p-19rpx text-#fff"
          style="background: rgba(0, 0, 0, 0.4)"
        >
          <view class="text-26rpx">{{ details.cname }}</view>
          <view class="flex gap-10rpx mt-20rpx">
            <view class="tag">{{ details.topCategoryName }}</view>
            <view class="tag">{{ details.cperiod }}学时</view>
          </view>
        </view>

        <view
          class="absolute bottom-0 right-0 text-#fff text-22rpx rounded-[20rpx_0_0_0] px-19rpx py-10rpx"
          :style="{ backgroundColor: getDeclareColor(details.creditStatus) }"
        >
          {{ DECLARE_STATUS[details.creditStatus] }}
        </view>
      </view>

      <view class="info !mt-30rpx">
        <text>真实姓名</text>
        <text>{{ details.realName }}</text>
      </view>

      <view class="info">
        <text>身份证号</text>
        <text>{{ details.cardno }}</text>
      </view>

      <view class="info">
        <text>工作单位</text>
        <text>{{ details.orgName }}</text>
      </view>

      <view class="info">
        <text>申请年份</text>
        <text v-if="details.year">{{ details.year }}</text>
        <wd-datetime-picker
          v-else
          type="year"
          v-model="year"
          :maxDate="new Date(details?.reportTime?.split(',').slice(-1)[0]).getTime()"
          :minDate="new Date(details?.reportTime?.split(',').slice(0, 1)[0]).getTime()"
          :default-value="new Date().getTime()"
        />
      </view>
    </view>

    <view
      v-if="!details.year && details.year !== undefined"
      class="absolute bottom-0 left-0 w-full"
    >
      <view class="flex justify-center mb-58rpx">
        <wd-button type="primary" @click="handleSubmit" class="w-600rpx !h-84rpx">
          提交申报
        </wd-button>
      </view>
      <view
        class="text-center h-68rpx lh-68rpx bg-#FEF8EB text-#DA6B2A text-24rpx shadow-[0rpx_0rpx_20rpx_0rpx_rgba(0,0,0,0.09)]"
      >
        请在申请前确认好申请年度，一旦申请不可修改
      </view>
    </view>

    <ky-dialog v-model:visible="showDialog" @confirm="handleConfirm" cancelText="关闭">
      <view class="text-left text-#333333 text-32rpx px-25rpx lh-54rpx">
        申报年度一经申报不可更改，是否要提交？
      </view>
    </ky-dialog>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import { DECLARE_STATUS, getDeclareColor } from '@/utils/dataStatus'
import { httpGet, httpPost } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'
import KyDialog from '@/components/KyDialog.vue'

const toast = useToast()
let id
const details = ref({})
const year = ref('')
const getDetails = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/credit/apply/certificateDetail', { id }),
  )
  details.value = res
}

const showDialog = ref(false)
const handleSubmit = async () => {
  showDialog.value = true
}

const handleConfirm = async () => {
  showDialog.value = false
  const { id, courseId, realName, cardno, orgName } = details.value

  await useRequestWrap(() =>
    httpPost('/study/portal/credit/apply/certificateGenerate', {
      year: new Date(year.value).getFullYear(),
      id,
      courseId,
      realName,
      cardno,
      orgName,
    }),
  )
  await getDetails()
  toast.success('申报成功')
}

onLoad(({ id: _id }) => {
  id = _id
  getDetails()
})
</script>

<style lang="scss" scoped>
.item {
  position: relative;
  display: flex;
  background-color: #fff;

  .tag {
    color: #fff;
    border-color: #fff;
    opacity: 0.8;
  }

  .sign {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10rpx 20rpx 8rpx;
    font-size: 22rpx;
    color: #fff;
    border-radius: 20rpx 0 20rpx 0;
  }
}

.info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 98rpx;
  padding: 0 22rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
  background: #fff;
  border-radius: 20rpx;
  & > text:first-child {
    color: #888888;
  }
  & > text:last-child {
    color: #333;
  }
}

:deep(.wd-picker__cell) {
  padding: 0 !important;
}
</style>
