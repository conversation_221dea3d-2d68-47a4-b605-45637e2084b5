export function formatDateTime(timestamp: number): string {
  // 将剩余的秒数转换为小时、分钟和秒
  let hours: number = Math.floor(timestamp / 3600)
  let minutes: number = Math.floor((timestamp % 3600) / 60)
  let secs: number = timestamp % 60

  // 补零，确保分钟和秒始终是两位数
  const paddedHours: string = hours < 10 ? '0' + hours : String(hours)
  const paddedMinutes: string = minutes < 10 ? '0' + minutes : String(minutes)
  const paddedSecs: string = secs < 10 ? '0' + secs : String(secs)

  return `${paddedHours}:${paddedMinutes}:${paddedSecs}`
}
