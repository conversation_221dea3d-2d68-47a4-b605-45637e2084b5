<route lang="json5">
{
  style: {
    navigationBarTitleText: '实名认证',
  },
}
</route>
<template>
  <view class="container bg-#ebeff0">
    <web-view :src="src" />
  </view>
</template>

<script setup>
import { useUserStore } from '@/store'

const { userInfo } = useUserStore()
const src = ref(
  `https://h5verify.populook.com/IdentetityCard?source=3&type=01&userid=${userInfo.userId}&sid=${import.meta.env.VITE_SITE_CODE}`,
)
</script>

<style lang="scss" scoped></style>
