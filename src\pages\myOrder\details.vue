<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单详情',
  },
}
</route>
<template>
  <view class="container !overflow-auto">
    <view class="wrap-style">
      <view>订单编号：{{ details.ordno }}</view>
      <view class="my-10rpx">下单时间：{{ details.ordpaytime || '-' }}</view>
      <view>订单状态：{{ getOrderStatus(details.ordstatus)?.text }}</view>
    </view>

    <view class="wrap-style mt-20rpx">
      <view class="flex justify-between">
        <text class="text-#B7B7B7">共{{ details.orderDetailVos?.length }}门</text>
        <text class="text-[var(--primary-color)]">
          <text>实付：</text>
          <text>￥{{ details.ordmoneysum }}</text>
        </text>
      </view>
      <view
        v-for="course in details.orderDetailVos"
        :key="course.id"
        class="my-20rpx flex gap-15rpx"
      >
        <ky-image
          :src="course.platformCourseVo.cimage"
          width="280rpx"
          height="98px"
          class="flex-shrink-0"
        />
        <view class="flex flex-col justify-between flex-1">
          <view class="text-30rpx">{{ course.platformCourseVo.cname }}</view>
          <view class="flex items-center justify-between">
            <text class="text-#999 text-27rpx">{{ course.platformCourseVo.period }}学时</text>
            <text class="text-[var(--primary-color)]">￥{{ course.platformCourseVo.price }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { getOrderStatus } from '@/utils/dataStatus'
import KyImage from '@/components/KyImage.vue'

const details = ref({})
const getDetails = async (id) => {
  const res = await useRequestWrap(() => httpGet('/study/portal/order/orderDetail', { id }))
  details.value = res
}

onLoad(({ id }) => {
  getDetails(id)
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  .wrap-style {
    padding: 20rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0 5rpx 20rpx 0 rgba(0, 0, 0, 0.06);
  }
}
</style>
