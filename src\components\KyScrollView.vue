<template>
  <z-paging ref="pagingRef" v-model="dataList" @query="queryList" :fixed="false">
    <slot :data="dataList"></slot>
  </z-paging>
</template>

<script setup>
const props = defineProps({
  apiFun: {
    type: Function,
    required: true,
  },
})

const dataList = ref([])
const pagingRef = ref(null)
const total = ref(0)

const queryList = async (pageNo, pageSize) => {
  const res = await useRequestWrap(() =>
    props.apiFun({
      size: pageSize,
      current: pageNo,
    }),
  )
  total.value = res.total
  pagingRef.value?.complete(res.records)
}

const getTotal = () => {
  return total.value
}

defineExpose({
  getTotal,
})
</script>

<style lang="scss" scoped></style>
