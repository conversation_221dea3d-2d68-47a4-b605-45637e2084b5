import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThemeStore = defineStore(
  'themeStore',
  () => {
    const theme = ref({
      '--primary-color': '#ff6700',
      '--wot-input-placeholder-color': '#C8C7C7',
    })
    const primaryColor = computed(() => theme.value['--primary-color'])

    const init = () => {}

    return {
      theme,
      primaryColor,
      init,
    }
  },
  { persist: true },
)
