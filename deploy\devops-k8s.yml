apiVersion: apps/v1
kind: Deployment
metadata:
  name: $APP_NAMESPACE-$APP_NAME
  namespace: $APP_NAMESPACE
  labels:
    app: $APP_NAMESPACE-$APP_NAME
    component: $APP_NAMESPACE
    tier: backend
spec:
  progressDeadlineSeconds: 600
  replicas: $REPLICAS
  selector:
    matchLabels:
      app: $APP_NAMESPACE-$APP_NAME
      component: $APP_NAMESPACE
      tier: backend
  template:
    metadata:
      labels:
        app: $APP_NAMESPACE-$APP_NAME
        component: $APP_NAMESPACE
        tier: backend
    spec:
      containers:
        - env:
            - name: CACHE_IGNORE
              value: js|html
            - name: CACHE_PUBLIC_EXPIRATION
              value: 3d
            - name: TRAILING_SLASH
              value: "false"
            - name: TZ
              value: Asia/Shanghai
          image: $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAMESPACE-$APP_NAME-$BRANCH_NAME:$BUILD_NUMBER
          imagePullPolicy: Always
          name: $APP_NAMESPACE-$APP_NAME
          volumeMounts:
            - name: sfere-time-zone
              mountPath: /usr/share/zoneinfo/Asia/Shanghai
              readOnly: true
          ports:
            - containerPort: $PORT
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: $PORT
            initialDelaySeconds: 20
            periodSeconds: 5
          livenessProbe:
            tcpSocket:
              port: $PORT
            initialDelaySeconds: 20
            periodSeconds: 15
          resources:
            limits:
              cpu: 1800m
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 500M
          terminationMessagePath: /$DEPLOY_TYPE/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      volumes:
        - name: sfere-time-zone
          hostPath:
            path: /etc/localtime
      terminationGracePeriodSeconds: 30
