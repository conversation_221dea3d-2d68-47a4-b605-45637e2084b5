<template>
  <svg aria-hidden="true" width="1em" height="1em">
    <use :xlink:href="symbolId" :fill="color" />
  </svg>
</template>

<script setup>
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  prefix: {
    type: String,
    default: 'icon',
  },
  color: {
    type: String,
    default: 'var(--primary-color)',
  },
})

const symbolId = computed(() => `#${props.prefix}-${props.icon}`)
</script>
