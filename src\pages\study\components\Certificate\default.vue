<template>
  <view
    class="px-20rpx py-30rpx space-y-20rpx overflow-auto"
    :style="{ height: getViewHeight('48rpx', '100rpx') }"
  >
    <view v-if="dataList.length" v-for="item in dataList" class="p-20rpx bg-#fff rounded-20rpx">
      <view class="flex items-center justify-center gap-8rpx">
        <image :src="getCdnImageUrl('/study/year.png')" mode="widthFix" class="w-27rpx h-27rpx" />
        <text class="text-28rpx font-bold text-[var(--primary-color)]">
          {{ item.year }}年申报情况
        </text>
      </view>
      <view
        class="flex justify-between text-24rpx h-64rpx bg-#F3F6F9 items-center rounded-32rpx px-31rpx mt-24rpx"
      >
        <view class="text-#333333">专业提升</view>
        <view
          class="text-#666666 flex gap-4rpx items-center"
          @click="handleClick('/pages/study/courseList', '专业提升', '02', item.year)"
        >
          <text>已申报</text>
          <text class="text-[var(--primary-color)] font-bold min-w-50rpx text-center">
            {{ item.zyCredit }}
          </text>
          <text>学时</text>
          <wd-icon name="chevron-right-circle" size="24rpx" class="ml-8rpx"></wd-icon>
        </view>
      </view>

      <view
        class="flex justify-between text-24rpx h-64rpx items-center rounded-32rpx px-31rpx mt-20rpx"
      >
        <view class="text-#333333">专业培训</view>
        <view
          class="text-#666666 flex gap-4rpx items-center"
          @click="handleClick('/pages/study/courseList', '专业培训', '04', item.year)"
        >
          <text>已申报</text>
          <text class="text-[var(--primary-color)] font-bold min-w-50rpx text-center">
            {{ item.pxCredit }}
          </text>
          <text>学时</text>
          <wd-icon name="chevron-right-circle" size="24rpx" class="ml-8rpx"></wd-icon>
        </view>
      </view>

      <view
        class="flex justify-between text-24rpx h-64rpx bg-#F3F6F9 items-center rounded-32rpx px-31rpx mt-24rpx"
      >
        <view class="text-#333333">课程选修</view>
        <view
          class="text-#666666 flex gap-4rpx items-center"
          @click="handleClick('/pages/study/courseList', '课程选修', '03', item.year)"
        >
          <text>已申报</text>
          <text class="text-[var(--primary-color)] font-bold min-w-50rpx text-center">
            {{ item.xxCredit }}
          </text>
          <text>学时</text>
          <wd-icon name="chevron-right-circle" size="24rpx" class="ml-8rpx"></wd-icon>
        </view>
      </view>
    </view>

    <wd-status-tip
      v-else
      image="content"
      tip="暂无内容"
      :image-size="{
        width: '500rpx',
      }"
    />
  </view>
</template>

<script setup>
import { getViewHeight } from '@/utils/getViewHeight'
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const dataList = ref([])
const getDataList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/archive/certificateStatisticsByYears'),
  )
  dataList.value = res
}

const handleClick = (url, title, topType, year) => {
  uni.setStorageSync('certificateTitle', title)
  toPage(url, { year, topType })
}

getDataList()
</script>

<style scoped lang="scss"></style>
