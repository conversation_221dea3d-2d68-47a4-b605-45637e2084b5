<route lang="json5">
{
  style: {
    navigationBarTitleText: '学时证明详情',
  },
}
</route>

<template>
  <div class="container p-20rpx bg-#fff">
    <div class="space-y-10rpx">
      <div class="text-#333 text-26rpx">
        <span class="text-#989898 inline-block">
          姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：
        </span>
        <span>{{ info.realname }}</span>
      </div>
      <div class="text-#333 text-26rpx">
        <span class="text-#989898">身份证号：</span>
        <span>{{ info.cardno }}</span>
      </div>
      <div class="text-#333 text-26rpx">
        <span class="text-#989898">工作单位：</span>
        <span>{{ userStore.userInfo.orgName }}</span>
      </div>
      <div class="text-#333 text-26rpx">
        <span class="text-#989898 inline-block">
          学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：
        </span>
        <span>{{ info.total }}</span>
      </div>
      <div class="text-#333 text-26rpx">
        <span class="text-#989898">申报年份：</span>
        <span>{{ info.applyYear }}</span>
      </div>
      <div class="text-#333 text-26rpx">
        <span class="text-#989898">学习时间：</span>
        <span>{{ info.beginStudyTime.split(' ')[0] }} - {{ info.endStudyTime.split(' ')[0] }}</span>
      </div>
    </div>

    <div class="text-28rpx text-#53b0e9 text-center mt-50rpx" @click="download(info)">
      下载学习证明
    </div>
    <wd-popup v-model="visible">
      <wd-img width="100vw" mode="widthFix" :src="src"></wd-img>
    </wd-popup>
  </div>
</template>

<script setup>
import { useUserStore } from '@/store'
import { httpGet } from '@/utils/uniHttp'

const userStore = useUserStore()
const { showLoading, hideLoading, toast } = useLoading()

const info = ref({})

const visible = ref(false)
const src = ref('')
const download = async (item) => {
  showLoading()
  try {
    const res = await useRequestWrap(() =>
      httpGet('/study/portal/archive/certificate/imageById', { id: item.id }),
    )
    src.value = res.imageBase64
    visible.value = true
    uni.showToast({
      icon: 'none',
      title: '长按图片保存到手机',
    })
  } finally {
    hideLoading()
  }
}

onLoad(({ id }) => {
  const certificateDetails = JSON.parse(uni.getStorageSync('certificateDetails'))
  const item = certificateDetails.find((item) => {
    console.log(' item-->', item)
    return item.id === id
  })
  info.value = item
})
</script>

<style lang="scss" scoped></style>
