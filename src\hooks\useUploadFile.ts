import { useRequestWrap } from '@/hooks/useRequestWrap'
import { httpUpload } from '@/utils/uniHttp'

// 在文件顶部添加接口定义
interface UploadResponse {
  createBy: string
  updateBy: string
  createTime: string
  updateTime: string
  id: string
  url: string
  size: string
  filename: string
  originalFilename: string
  basePath: string
  path: string
  ext: string
  platform: string
  thUrl: string | null
  thFilename: string | null
  thSize: string | null
  objectId: string | null
  objectType: string | null
  tenantCode: string
  downloadUrl: string
  previewUrl: string
  thumbnailUrl: string
}

// 上传文件
export const useUploadFile = (count = 1) => {
  const upload = (): Promise<UploadResponse> => {
    return new Promise((resolve, reject) => {
      uni.chooseImage({
        count,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const filePath = res.tempFilePaths[0] // 获取选择的文件路径
          resolve(
            await useRequestWrap<UploadResponse>(() =>
              httpUpload('/file/attachment/upload/one', filePath),
            ),
          )
        },
      })
    })
  }
  return {
    upload,
  }
}
