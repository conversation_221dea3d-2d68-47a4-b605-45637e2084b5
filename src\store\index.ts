import * as Pinia from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate' // 数据持久化

const store = Pinia.createPinia()
store.use(
  createPersistedState({
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  }),
)

export default store
export { Pinia }

// 模块统一导出
export * from './user'
export * from './theme'
export * from './exam'
export * from './app'
