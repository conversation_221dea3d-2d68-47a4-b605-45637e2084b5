<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '首页',
  },
  meta: {
    isTabBarPage: true,
  },
}
</route>
<template>
  <!-- #ifndef MP-WEIXIN -->
  <component :is="component" />
  <!-- #endif -->
  <!-- #ifdef MP-WEIXIN -->
  <defaultComponent />
  <!-- #endif -->
</template>

<script setup>
import { useSiteComponent } from '@/hooks/useSiteComponent'
import defaultComponent from './default.vue'

let modules
let component

const type = uni.getSystemInfoSync().uniPlatform
if (type === 'web') {
  // 在组件中获取当前目录的所有Vue文件
  modules = import.meta.glob('./*.vue')
  const siteComponent = useSiteComponent(modules)
  component = siteComponent.component
}
</script>
