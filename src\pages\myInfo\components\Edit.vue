<template>
  <wd-message-box selector="edit-userinfo-box">
    <view class="text-left text-30rpx mb-5rpx">
      {{ editField.title }}
    </view>
    <!-- 免冠半身照 -->
    <view
      v-if="editField.field === 'officialPhotoUrl'"
      class="w-300rpx h-500rpx mt-20rpx rounded-12rpx border-3rpx border-solid border-[var(--primary-color)] mx-auto overflow-hidden relative"
    >
      <view class="w-full h-full" @click="editOfficialPhotoUrl">
        <ky-image :src="editValue" width="100%" height="100%" />
      </view>
      <wd-icon
        name="add"
        size="22px"
        color="var(--primary-color)"
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-bold"
      ></wd-icon>
    </view>
    <!-- 姓名、单位名称 -->
    <wd-input
      v-else-if="['realName', 'orgName', 'address', 'postcode'].includes(editField.field)"
      v-model="editValue"
      :placeholder="`请输入${editField.title}`"
    />
    <!-- 性别 -->
    <wd-radio-group v-else-if="editField.field === 'sex'" v-model="editValue" shape="dot">
      <wd-radio v-for="item in sexOptions" :value="item.value">
        {{ item.label }}
      </wd-radio>
    </wd-radio-group>
  </wd-message-box>
</template>

<script setup lang="ts">
import KyImage from '@/components/KyImage.vue'
import { useUserStore } from '@/store'
import { GENDER } from '@/utils/dataStatus'
import { useMessage } from 'wot-design-uni'
import { editUserInfo } from '../helpr'
interface EditField {
  field: string
  title: string
  value: string
}

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps<{
  editField: EditField
}>()

const message = useMessage('edit-userinfo-box')
const userStore = useUserStore()

const editValue = ref('')

const openEntry = () => {
  open()
}
const open = () => {
  editValue.value = props.editField.value
  message
    .confirm({
      closeOnClickModal: false,
    })
    .then(() => {
      // 免冠半身照
      if (props.editField.field === 'officialPhotoUrl') {
        editUserInfo({ officialPhoto }, () => {
          userStore.getUserInfo()
        })
      } else {
        // 其他
        editUserInfo({ [props.editField.field]: editValue.value }, () => {
          userStore.getUserInfo()
        })
      }
    })
    .catch(() => {})
}

// 免冠半身照
const { upload } = useUploadFile(1)
let officialPhoto = ''
const editOfficialPhotoUrl = async () => {
  const res = await upload()
  editValue.value = res.url
  officialPhoto = res.id
}
// 性别
const sexOptions = Object.entries(GENDER).map(([key, value]) => ({
  label: value,
  value: key,
}))

defineExpose({
  open: openEntry,
})
</script>

<style lang="scss" scoped>
:deep(.wd-message-box__content) {
  max-height: 100vh !important;
  overflow: hidden !important;
}
:deep(.wd-button) {
  width: fit-content;
  padding: 42rpx 0 !important;
}
</style>
