<template>
  <wd-config-provider :theme-vars="themeVars" :custom-style="objectToString(cssVars)" class="main">
    <slot></slot>
  </wd-config-provider>
</template>

<script setup>
import { useThemeStore } from '@/store/theme'
import { shouldHideNavBar } from '@/utils/browser'

defineOptions({
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
})

const themeStore = useThemeStore()
const sysInfo = uni.getSystemInfoSync()
// 底部安全区高度
const bottomSafeAreaHeight = sysInfo.screenHeight - sysInfo.safeArea.bottom

const cssVars = ref({
  '--other-height': '0px',
  '--status-bar-height': sysInfo.statusBarHeight + 'px',
  '--bottom-safe-area-height': bottomSafeAreaHeight + 'px',
  ...themeStore.theme,
})
watch(
  () => themeStore.theme,
  () => {
    Object.assign(cssVars.value, themeStore.theme)
  },
  {
    deep: true,
    immediate: true,
  },
)

// 获取胶囊按钮信息
// #ifdef MP-WEIXIN
const { width } = wx.getMenuButtonBoundingClientRect()
cssVars.value['--capsule-width'] = width + 'px'
// #endif

const themeVars = computed(() => ({
  colorTheme: themeStore.theme['--primary-color'],
  cellArrowColor: '#00000040',
}))

function objectToString(obj) {
  return Object.entries(obj)
    .map(([key, value]) => `${key}:${value}`)
    .join('; ')
}

onShow(() => {
  setTimeout(() => {
    // 如果隐藏了导航栏则需要加上导航栏的高度，使页面高度正常
    if (shouldHideNavBar()) {
      cssVars.value['--other-height'] = '44px'
    } else {
      cssVars.value['--other-height'] = '0px'
    }

    cssVars.value['--window-height'] = uni.getSystemInfoSync().windowHeight + 'px'
  }, 60)
})
</script>

<style lang="scss" scoped></style>
