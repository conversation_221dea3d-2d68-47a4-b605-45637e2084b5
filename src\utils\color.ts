/**
 * 颜色转换工具函数
 */

import { useThemeStore } from '@/store'

/**
 * 将十六进制颜色转换为rgba格式
 * @param hexColor 十六进制颜色，如 #ff6700 或 ff6700
 * @param opacity 透明度，默认为0.3
 * @returns rgba格式的颜色，如 rgba(255,103,0,0.3)
 */
export function hexToRgba(hexColor: string, opacity: number = 0.3): string {
  // 去掉可能存在的#前缀
  const hex = hexColor.replace('#', '')

  // 将十六进制转换为RGB值
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  // 返回rgba格式
  return `rgba(${r},${g},${b},${opacity})`
}

/**
 * 根据主题色生成带透明度的rgba颜色
 * @param themeColor 主题色，如 #ff6700
 * @returns 转换后的rgba颜色
 */
export function getThemeColorWithOpacity(themeColor: string): string {
  const themeStore = useThemeStore()
  if (!themeColor) {
    themeColor = themeStore.primaryColor
  }
  return hexToRgba(themeColor, 0.3)
}
