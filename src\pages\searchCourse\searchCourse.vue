<route lang="json5">
{
  style: {},
}
</route>
<template>
  <view class="container grid grid-rows-[auto_1fr] gap-20rpx">
    <view class="top-wrap flex items-center gap20rpx">
      <wd-input
        v-model="searchValue"
        prefix-icon="search"
        class="flex-1 rounded-99px"
        placeholder="搜索课程"
        focus
        @confirm="handleSearch(searchValue)"
      />
      <view class="text-[var(--primary-color)] text-26rpx" @click="handleCancel">取消</view>
    </view>
    <!-- 搜索历史 -->
    <view v-if="!showList" class="p-20rpx bg-#fff">
      <view class="flex items-center justify-between">
        <view class="font-bold text-32rpx text-[rgba(51,51,51,1)]">搜索历史</view>
        <wd-icon name="delete1" size="36rpx" color="#999" @click="searchHistory = []"></wd-icon>
      </view>
      <view class="flex gap-20rpx mt-20rpx flex-wrap">
        <view
          v-for="item in searchHistory"
          :key="item"
          class="bg-[rgba(244,247,250,1)] text-[rgba(153,153,153,1)] text-24rpx py-10rpx px-20rpx rounded-99rpx"
          @click="handleSearch(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>
    <view v-else class="p-20rpx bg-#fff grid grid-rows-[auto_1fr] overflow-hidden gap-20rpx">
      <view class="flex items-center justify-between">
        <view class="font-bold text-32rpx text-[rgba(51,51,51,1)]">
          搜索到
          <text class="text-[var(--primary-color)]">{{ total }}</text>
          门课程
        </view>
      </view>
      <ky-scroll-view ref="scrollViewComp" :api-fun="apiFun">
        <template #default="{ data }">
          <view class="grid grid-cols-2 gap-20rpx">
            <view
              v-for="item in data"
              @click="toPage('/pages/course/details', { id: item.courseId })"
            >
              <KyImage :src="item.cimage" class="w-full h-100px"></KyImage>
              <view>
                <view class="text-28rpx text-[var(--primary-color)] ellipsis">
                  {{ item.cname }}
                </view>
                <view class="text-22rpx text-[rgba(153,153,153,1)] ellipsis my-10rpx">
                  适用于 {{ item.reportTime }} 等年
                </view>
                <view class="flex items-center justify-between gap-20rpx">
                  <view
                    class="grid grid-cols-[1fr_auto_auto] items-center gap10rpx text-22rpx text-[rgba(153,153,153,1)]"
                  >
                    <text class="ellipsis">{{ item.topCategoryName || '-' }}</text>
                    <text>|</text>
                    <text class="ellipsis">{{ item.period || '-' }} 学时</text>
                  </view>
                  <view class="text-[var(--primary-color)]">￥{{ item.price }}</view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </ky-scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import KyImage from '@/components/KyImage.vue'
import KyScrollView from '@/components/KyScrollView.vue'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

const searchValue = ref('')
const searchHistory = ref([])

watch(searchHistory, (val) => {
  uni.setStorageSync('searchHistory', val)
})
const showList = ref(false)

const handleCancel = () => {
  searchValue.value = ''
  if (showList.value) {
    showList.value = false
  } else {
    uni.navigateBack()
  }
}

const scrollViewComp = ref(null)
const total = computed(() => scrollViewComp.value?.getTotal() || 0)

const addSearchHistory = (keyword: string) => {
  const uniqueHistory = new Set([keyword, ...searchHistory.value])
  searchHistory.value = Array.from(uniqueHistory)
}
const handleSearch = (val: string) => {
  if (!val) return
  showList.value = false
  searchValue.value = val
  addSearchHistory(val)
  nextTick(() => {
    showList.value = true
  })
}
const apiFun = (params: any) => {
  params.cname = searchValue.value
  return httpGet('/study/portal/course/page', params)
}

onShow(() => {
  searchHistory.value = uni.getStorageSync('searchHistory') || searchHistory.value
})
</script>

<style lang="scss" scoped>
.top-wrap {
  padding: 20rpx 30rpx;
  background: #fff;

  :deep(.wd-input) {
    .wd-input__body {
      padding-left: 10px;
      background: #f3f5f9;
      border: none;
      border-radius: 99px;

      .wd-icon {
        background: #f3f5f9;
        transform: translateY(-2px);
      }
    }

    &::after {
      display: none;
    }
  }
}
</style>
