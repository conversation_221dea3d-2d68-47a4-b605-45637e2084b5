<template>
  <view v-show="isShow" @click.stop="handleButtonClick" :class="props.customClass">
    <wd-button :custom-class="buttonConfig.className" size="small">
      {{ buttonConfig.text }}
    </wd-button>
  </view>

  <!-- #ifndef MP-WEIXIN -->
  <Teleport to=".main" :disabled="!hasMainElement">
    <ky-dialog v-model:visible="hintVisible" title="提示" @confirm="handleHintConfirm">
      <div class="text-#333">
        学习时间不允许重叠，一门课程学完后才能继续学习 下一门课程，现在是去学习上一个未完成的课程？
      </div>
    </ky-dialog>
  </Teleport>
  <!-- #endif -->
</template>

<script setup>
// 课程状态(前台) courseStatus: 01还未加入购物车 02已加入购物车 03还没支付完全 04还没开始学习 05已开始学习 06重新学习 07冻结
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import { computed, ref, onMounted } from 'vue'
import KyDialog from '@/components/KyDialog.vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  course: {
    type: Object,
    default: () => {},
  },
  customClass: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['study'])

// 检查.main元素是否存在
const hasMainElement = ref(false)

onMounted(() => {
  // 检查页面中是否存在.main元素
  hasMainElement.value = !!document?.querySelector('.main')
})

const isShow = computed(() => {
  return props.course.topCategoryName !== '初聘培训'
})

// 按钮配置对象
const buttonConfig = computed(() => {
  const configs = {
    '01': {
      text: '加入购物车',
      handler: handleAddCart,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    },
    '02': {
      text: '去购物车',
      handler: handleGoCart,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800' + props.customClass,
    },
    '03': {
      text: '去支付',
      handler: handleGoPay,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    },
    '04': {
      text: '去学习',
      handler: handleGoStudy,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    },
    '05': {
      text: '去学习',
      handler: handleGoStudy,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    },
    '06': {
      text: '去学习',
      handler: handleGoStudy,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    },
  }
  if (props.course.classify === '02') {
    return {
      text: '立即购买',
      handler: handleGoBuy,
      className: '!min-w-60rpx !w-full !h-full !m0 !bg-#ffa800 ' + props.customClass,
    }
  } else {
    return configs[props.course.courseStatus] || {}
  }
})
const handleButtonClick = (e) => {
  buttonConfig.value.handler?.(e)
}

// 加入购物车
const handleAddCart = async (e) => {
  await useRequestWrap(() =>
    httpGet('/study/portal/user/shop/car/addCourse', { courseId: props.course.courseId }),
  )
  props.course.courseStatus = '02'
}

// 去购物车
const handleGoCart = (e) => {
  uni.navigateTo({
    url: '/pages/shoppingCart/shoppingCart',
  })
}

// 去支付
const handleGoPay = (e) => {
  toPage('/pages/payOrder/payOrder', { id: props.course.orderId })
}

// 去学习
const hintVisible = ref(false)
let needStudyCourseId
const handleGoStudy = async (e) => {
  // console.log('props.course', props.course.)

  if (import.meta.env.VITE_SITE_NAME === 'hnndsydw' && props.course.courseStatus !== '06') {
    const res = await httpGet('/study/portal/play/hasOtherXxz?courseId=' + props.course.courseId)
    if (res.code === 0) {
      // 还有未学完的课程
      needStudyCourseId = res.data
      hintVisible.value = true
      return
    }
  }

  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentRoute = currentPage.route
  if (currentRoute === 'pages/course/details') {
    emit('study')
    return
  }
  toPage('/pages/course/details', { id: props.course.courseId, studyFlag: true })
}
const handleHintConfirm = () => {
  toPage(`/pages/course/details`, { id: needStudyCourseId, studyFlag: true })
  hintVisible.value = false
}

// 立即购买
const handleGoBuy = async (e) => {
  const id = await useRequestWrap(() =>
    httpGet('/study/portal/order/savePackageOrder', { packageId: props.course.courseId }),
  )
  toPage('/pages/payOrder/payOrder', { id })
}

defineExpose({
  isShow,
})
</script>

<style lang="scss" scoped></style>
