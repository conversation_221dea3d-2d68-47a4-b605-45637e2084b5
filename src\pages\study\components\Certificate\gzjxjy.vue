<template>
  <div class="p-20rpx h-full box-border">
    <z-paging ref="pagingRef" v-model="dataList" @query="queryList" :fixed="false">
      <div class="bg-#fff rounded-15rpx p-30rpx shadow">
        <div class="flex justify-around">
          <div class="flex flex-col items-center">
            <span class="text-24rpx text-#666">总学时</span>
            <span class="font-bold text-46rpx">{{ certificateInfo.allCredit }}</span>
          </div>
          <div class="flex flex-col items-center">
            <span class="text-24rpx text-#666">已申报</span>
            <span class="font-bold text-46rpx text-#52af62">
              {{ certificateInfo.hasCredit }}
            </span>
          </div>
          <div class="flex flex-col items-center">
            <span class="text-24rpx text-#666">未申报</span>
            <span class="font-bold text-46rpx text-#ff5c0f">{{ certificateInfo.unCredit }}</span>
          </div>
        </div>
        <div>
          <wd-button class="w-full mt-20rpx mb-10rpx" @click="yearVisible = true">
            立即申报
          </wd-button>
          <div class="text-right text-#ff5c0f text-26rpx" @click="hintVisible = true">
            查看温馨提示
          </div>
        </div>
      </div>

      <div
        v-for="item in dataList"
        class="relative bg-#fff p-30rpx text-center shadow rounded-15rpx my-30rpx"
        @click="toDetail(item)"
      >
        <div
          class="absolute top-0 left-0 rounded-[15rpx_0_30rpx_0] bg-#ff5c0e text-26rpx text-#fff p-[4rpx_30rpx_7rpx_20rpx]"
        >
          {{ item.year }}
        </div>
        <div class="relative">
          <div class="text-24rpx text-#666">总学时</div>
          <div class="font-bold text-46rpx text-#ff6700 mt-10rpx">{{ item.creditnum }}</div>
          <wd-icon
            name="arrow-right"
            size="20px"
            color="var(--wot-cell-arrow-color)"
            class="absolute right-0rpx top-50% -translate-y-50%"
          ></wd-icon>
        </div>
      </div>
    </z-paging>
  </div>
  <!-- 温馨提示 -->
  <ky-dialog
    title="温馨提示"
    v-model:visible="hintVisible"
    :showCancelButton="false"
    @confirm="hintVisible = false"
  >
    <div class="text-left">
      申报中级继续教育每年32学时，5年累计达到160 学时以上：初级继续教育学时累计达到60学时以
      上。乡、镇（不含城关镇）和民营经济组织的专 业技术人员的继续教育学时要求适当放宽，申报
      中级专业技术职务任职资格的年累计完成100学 时；初级累计完成40学时。具体所需学时按评审
      部门要求执行，请您根据自身情况打印学习证明， 若总学时不足请继续学习！
    </div>
  </ky-dialog>
  <wd-action-sheet
    v-model="yearVisible"
    title="申请证书"
    cancel-text="取消"
    :actions="yearList"
    @select="select"
  >
    <view class="text-24rpx text-#e2662e px-40rpx mb-20rpx">
      提醒:请先选择证书申请年份，然后进入申请。请您确认好申请年度，证书一旦生成无法进行修改。
    </view>
  </wd-action-sheet>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

const hintVisible = ref(false)
const yearVisible = ref(false)
const yearList = ref([])
const getYearList = async () => {
  const res = await useRequestWrap(() => httpGet('/admin/portal/config/declareYears'))
  res.map((item) => {
    yearList.value.push({
      name: item,
    })
  })
}
const select = ({ item }) => {
  toPage('/pages/apply/apply', { year: item.name })
}

const dataList = ref([])
const pagingRef = ref(null)
const queryList = async (pageNo, pageSize) => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/archive/creditArchives/gzYear', { size: pageSize, current: pageNo }),
  )
  certificateInfo.value = res
  certificateInfo.value.creditStatics.forEach((item, index) => {
    item.key = index
  })
  // total.value = res.total
  pagingRef.value?.complete(res.creditStatics)
}

const certificateInfo = ref({})
const getStudyCertificateList = async (current = 1) => {
  const res = await useRequestWrap(() => httpGet('/study/portal/archive/creditArchives/gzYear'))
  certificateInfo.value = res
  certificateInfo.value.creditStatics.forEach((item, index) => {
    item.key = index
  })
}

const toDetail = (item) => {
  uni.setStorageSync('certificateDetails', JSON.stringify(item.certificateList))
  toPage('/pages/certificateDetails/certificateDetails', { year: item.year })
}

// getStudyCertificateList()
getYearList()
</script>

<style lang="scss" scoped>
:deep(.wd-popup--bottom) {
  bottom: 60px;
}
</style>
