<template>
  <view>
    <image
      :src="getCdnImageUrl('/home/<USER>')"
      mode="widthFix"
      class="w33rpx translate-y-[-5rpx]"
      @click="show = true"
    />

    <wd-popup v-model="show" position="left" :close-on-click-modal="false">
      <view class="w-72vw" :class="isH5 && 'mt50px'">
        <view v-for="item in screeningCondition" class="mb20rpx font-600 px-20rpx">
          <view class="mb10rpx">{{ item.title }}</view>
          <view class="grid grid-cols-[1fr_1fr_1fr] gap-col-20rpx">
            <wd-button
              v-for="con in item.list"
              :custom-class="`btn ${active[item.filed] === con.value && 'active'}`"
              size="small"
              @click="active[item.filed] = con.value"
            >
              {{ con.label }}
            </wd-button>
          </view>
        </view>

        <view
          class="w-full flex justify-center position-absolute box-border gap-20rpx"
          :class="isH5 ? 'bottom-130rpx' : 'bottom-20rpx'"
        >
          <wd-button type="info" @click="reset" custom-class="!m0 p0">重置</wd-button>
          <wd-button @click="confirm" custom-class="!m0 p0">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { getCdnImageUrl } from '@/utils/images'
import PLATFORM from '@/utils/platform'
defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const emits = defineEmits(['confirm'])
const { isH5 } = PLATFORM
const show = ref(false)
const screeningCondition = [
  {
    title: '学时',
    filed: 'period',
    list: [
      { label: '全部', value: '' },
      { label: '1-5学时', value: '1-5' },
      { label: '6-10学时', value: '6-10' },
      { label: '11-15学时', value: '11-15' },
      { label: '16-20学时', value: '16-20' },
      { label: '20以上', value: '20' },
    ],
  },
  {
    title: '适用年份',
    filed: 'year',
    list: [{ label: '全部', value: '' }],
  },
  {
    title: '排序',
    filed: 'ordertype',
    list: [
      { label: '最新', value: '2' },
      { label: '好评', value: '8' },
      { label: '价格', value: '5' },
    ],
  },
]
const active = ref({
  period: '',
  year: '',
  ordertype: '2',
})

const reset = () => {
  active.value = {
    period: '',
    year: '',
    ordertype: '2',
  }
}
const confirm = () => {
  emits('confirm', active.value)
  show.value = false
}
</script>

<style scoped lang="scss">
:deep(.btn) {
  width: 100%;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  color: #7a7a7a !important;
  background: #f4f5f9 !important;
  transition: 0.1s;
}

:deep(.active) {
  color: var(--primary-color) !important;
  background: rgba(255, 103, 0, 0.2) !important;
  border: 1rpx solid var(--primary-color) !important;
}
</style>
