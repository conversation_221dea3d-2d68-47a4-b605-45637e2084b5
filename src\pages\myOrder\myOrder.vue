<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的订单',
  },
}
</route>
<template>
  <view class="container">
    <KyScrollViewV2
      ref="scrollViewRef"
      v-model:current="current"
      :tabList="tabList"
      :apiFun="getDataList"
    >
      <template #default="{ data }">
        <view v-for="item in data" :key="item.id" class="bg-#fff my-20rpx p-20rpx">
          <view class="flex items-center justify-between text-#B7B7B7 text-26rpx">
            <text>订单号：{{ item.ordno }}</text>
            <text>
              <text :class="getOrderStatus(item.ordstatus).color">
                {{ getOrderStatus(item.ordstatus).text }}
              </text>
            </text>
          </view>
          <view
            v-for="course in item.orderDetailVos"
            :key="course.id"
            class="my-20rpx flex gap-15rpx"
          >
            <ky-image
              :src="course.platformCourseVo?.cimage"
              width="280rpx"
              height="98px"
              class="flex-shrink-0"
            />
            <view class="flex flex-col justify-between flex-1">
              <view class="text-30rpx">{{ course.platformCourseVo?.cname }}</view>
              <view class="flex items-center justify-between">
                <text class="text-#999 text-27rpx">{{ course.platformCourseVo?.period }}学时</text>
                <text class="text-[var(--primary-color)]">
                  ￥{{ course.platformCourseVo?.price }}
                </text>
              </view>
              <view
                v-if="
                  item.ordstatus == '20' &&
                  course.platformCourseVo.studyStatus === '01' &&
                  refundChange_show.includes(siteName)
                "
                class="flex gap-20rpx justify-end"
              >
                <confirm-button
                  size="small"
                  plain
                  type="info"
                  customClass="!px-40rpx ml-20rpx !m0"
                  msg="确定要退款吗？"
                  @confirm="handleRefund(course)"
                >
                  退款
                </confirm-button>
                <wd-button
                  size="small"
                  plain
                  type="info"
                  class="!px-40rpx ml-20rpx !m0"
                  @click="toPage('/pages/changeCourse/changeCourse?id=' + course.courseid)"
                >
                  换课
                </wd-button>
              </view>
            </view>
          </view>
          <view class="text-right mt-40rpx">
            <view class="text-30rpx">
              <text class="text-#B7B7B7 mr-[-15rpx]">
                共{{ item.orderDetailVos.length }}门 实付：
              </text>
              <text class="text-[var(--primary-color)]">￥{{ item.ordmoneysum }}</text>
              <text v-if="item.dropmoneysum > 0" class="text-#ff8d17">
                ({{
                  item.dropmoneysum === item.ordmoneysum ? '全额退款' : `退款${item.dropmoneysum}`
                }})
              </text>
            </view>
            <view class="mt-20rpx flex justify-end gap-20rpx">
              <wd-button
                size="small"
                plain
                type="info"
                class="!px-40rpx ml-20rpx !m0"
                @click="toPage('/pages/myOrder/details?id=' + item.id)"
              >
                查看
              </wd-button>
              <wd-button
                v-if="item.ordstatus == '01'"
                size="small"
                plain
                type="info"
                class="!px-40rpx ml-20rpx !m0"
                @click="cancelOrder(item.id)"
              >
                取消
              </wd-button>
              <confirm-button
                v-if="item.ordstatus == '40'"
                size="small"
                plain
                type="error"
                custom-class="!px-40rpx ml-20rpx !m0"
                @confirm="confirmDelete(item.id)"
              >
                删除
              </confirm-button>
              <wd-button
                v-if="item.ordstatus == '01'"
                size="small"
                plain
                class="!px-40rpx !m0"
                @click="toPage('/pages/payOrder/payOrder?id=' + item.id)"
              >
                支付
              </wd-button>
            </view>
          </view>
        </view>
      </template>
    </KyScrollViewV2>
  </view>
</template>

<script setup>
// 订单状态(01:未支付,20:支付成功(01:未支付,20:已支付,40:已取消))
import ConfirmButton from '@/components/ConfirmButton.vue'
import KyImage from '@/components/KyImage.vue'
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { refundChange_show } from '@/config'
import { getOrderStatus } from '@/utils/dataStatus'
import { toPage } from '@/utils/toPage'
import { httpGet, httpPost } from '@/utils/uniHttp'

const siteName = import.meta.env.VITE_SITE_NAME
const current = ref(0)
const tabList = ref(['全部', '未支付', '已支付', '已取消'])
const tabsValue = ref(['', '01', '20', '40'])
const scrollViewRef = ref(null)
const getDataList = async (params) => {
  return httpPost(
    '/study/portal/order/getPage',
    { ordstatus: tabsValue.value[current.value] },
    params,
  )
}

// 删除订单
const confirmDelete = async (id) => {
  await useRequestWrap(() => httpGet('/study/portal/order/delOrder', { id }))
  scrollViewRef.value.refresh()
  uni.showToast({
    title: '删除成功',
    icon: 'none',
  })
}
// 取消
const cancelOrder = async (id) => {
  await useRequestWrap(() => httpGet('/study/portal/order/cancelOrder', { id }))
  scrollViewRef.value.refresh()
  uni.showToast({
    title: '取消成功',
    icon: 'none',
  })
}
// 退款
const handleRefund = async (item) => {
  console.log(' item -->', item)
  await useRequestWrap(() =>
    httpGet('/study/portal/course/unsubscribe', {
      courseId: item.courseid,
    }),
  )
  uni.showToast({
    title: '退课成功',
    icon: 'none',
  })
  scrollViewRef.value.refresh()
}

onShow(() => {
  scrollViewRef.value?.refresh()
})
</script>

<style lang="scss" scoped>
:deep(.is-active) {
  color: var(--primary-color);
}
</style>
