<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '智能选课',
  },
}
</route>
<template>
  <view class="overflow-auto container flex flex-col">
    <view
      class="space-y-20rpx p-30rpx mx-17rpx mt-30rpx rounded-20rpx"
      style="background: linear-gradient(0deg, #fff 40%, #fff4e9 100%)"
    >
      <view class="flex items-center px-10rpx py-5rpx rounded-10rpx text-#333">
        <view class="text-#333 text-28rpx">目标学时：</view>
        <wd-input type="text" v-model="period" placeholder="请输入用户名" class="flex-1" />
      </view>
      <view class="flex items-center px-10rpx py-5rpx rounded-10rpx text-#333">
        <view class="text-#333 text-28rpx">课程类目：</view>
        <view class="flex-1" @click="handleSelect">
          <!-- selectKey.length === 0 -->
          <wd-input
            v-if="selectKey.length === 0"
            type="text"
            placeholder="请选择课程类目"
            readonly
          />
          <view
            v-else
            class="min-h-34px bg-#fff rounded-10rpx px-15rpx py-10rpx box-border flex gap-17rpx flex-wrap"
            style="border: 1px solid #dddddd"
          >
            <view
              v-for="(item, index) in selectKey.slice(0, 2)"
              :key="item.id"
              class="text-26rpx bg-#F3F3F3 px-11rpx py-9rpx lh-1em text-#333"
            >
              {{ item.categoryName }}
            </view>
            <view
              v-if="selectKey.length > 2"
              class="text-26rpx bg-#F3F3F3 px-11rpx py-9rpx lh-1em text-[var(--primary-color)]"
            >
              +{{ selectKey.length }}
            </view>
          </view>
        </view>
      </view>
      <view class="text-center">
        <wd-button class="!rounded-10rpx !h-74rpx mt-5rpx" @click="handleScreen">
          筛选课时
        </wd-button>
      </view>
    </view>

    <view v-if="courseList.length" class="px-20rpx my-35rpx space-y-20rpx">
      <wd-swipe-action v-for="item in courseList" :key="item.id" class="bg-#fff rounded-20rpx">
        <view class="pl-35rpx py-25rpx pr-30rpx flex item-center gap-34rpx">
          <ky-image :src="item.cimage" width="235rpx" height="133rpx" mode="widthFix" />
          <view class="flex flex-col justify-between flex-1">
            <view class="text-#333 text-28rpx font-bold">{{ item.cname }}</view>
            <view class="flex flex-col text-24rpx text-#888888">
              <view>{{ item.topCategoryName }} &nbsp; | &nbsp; {{ item.period }}学时</view>
              <view class="flex justify-between w-full items-end">
                <text>适用于{{ item.reportTime }}年</text>
                <text class="text-#FF6700 text-28rpx">{{ item.price }}元</text>
              </view>
            </view>
          </view>
        </view>
        <template #right>
          <view class="flex h-full ml-2rpx">
            <view
              class="flex items-center justify-center h-full text-24rpx text-#fff bg-#ffa800 px-24rpx"
              @click="handleChange(item)"
            >
              换一换
            </view>
            <view
              class="flex items-center justify-center h-full text-24rpx text-#fff bg-#ff002a px-24rpx"
              @click="handleDelete(item)"
            >
              删除
            </view>
          </view>
        </template>
      </wd-swipe-action>
    </view>

    <view v-else class="flex items-center justify-center flex-1 flex-col px-100rpx">
      <image src="@/static/course/znxk.png" mode="widthFix" />
      <text class="text-#999999 text-28rpx mt-10rpx">智能选课，帮您选择</text>
    </view>

    <!-- 底部 -->
    <view class="h-100rpx flex-shrink-0"></view>
    <view
      class="bg px-20rpx h-100rpx flex-shrink-0 flex items-center justify-between fixed bottom-0 w-100vw box-border"
    >
      <view class="flex items-center gap-20rpx text-24rpx">
        <view>
          <text>已选学时:</text>
          <text class="text-#ed712e font-bold">{{ selectedInfo.period }}</text>
          <text>学时</text>
        </view>
        <view>
          <text>金额:</text>
          <text class="text-#ed712e font-bold">{{ selectedInfo.amount }}</text>
          <text>元</text>
        </view>
      </view>
      <view class="h-68rpx">
        <wd-button
          class="!rounded-10rpx !bg-transparent !min-w-68rpx !h-68rpx !p-0"
          plain
          @click="handleClick"
        >
          <view class="flex items-center">
            <SvgIcon icon="shopping_cart" width="22px" height="22px" />
          </view>
        </wd-button>
        <wd-button
          size="small"
          class="!h-full !rounded-10rpx !text-24rpx ml-20rpx"
          @click="handlePay"
        >
          去结算
          <text v-if="selectedInfo.period">{{ selectedInfo.period }}学时</text>
        </wd-button>
      </view>

      <!-- <view class="flex h-full gap-1rpx">
        <wd-button
          size="small"
          class="!h-full !rounded-0rpx !text-24rpx"
          plain
          @click="handleClick"
        >
          加入购物车
        </wd-button>
        <wd-button size="small" class="!h-full !rounded-0rpx !text-24rpx" @click="handlePay">
          去结算
          <text v-if="selectedInfo.period">{{ selectedInfo.period }}学时</text>
        </wd-button>
      </view> -->
    </view>

    <ba-tree-picker
      ref="treePicker"
      :multiple="true"
      :localdata="listData"
      title="请选择课程类目"
      valueKey="id"
      textKey="categoryName"
      childrenKey="children"
      confirmColor="#ff6700"
      cancelColor="#999"
      titleColor="#333"
      switchColor="#666"
      :border="true"
      @select-change="selectChange"
    />
    <!-- 设置培训类型 -->
    <SelectPayType v-model:visible="selectPayTypeVisible" @confirm="handleSelectTypeConfirm" />
  </view>
</template>

<script setup>
import baTreePicker from '@/components/ba-tree-picker/ba-tree-picker.vue'
import KyImage from '@/components/KyImage.vue'
import SelectPayType from '@/components/SelectPayType.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { selectPayType } from '@/config'
import { toPage } from '@/utils/toPage'
import { httpGet, httpPost } from '@/utils/uniHttp'

const siteName = import.meta.env.VITE_SITE_NAME
const period = ref('')
const listData = ref([])
const treePicker = ref(null)
const selectKey = ref([])
const { showLoading, hideLoading, toast } = useLoading()
const courseList = ref([])
const selectedInfo = reactive({
  period: 0,
  amount: 0,
})
const selectChange = (data) => {
  selectKey.value = []

  const findSelectedCategories = (categories) => {
    categories.forEach((item) => {
      // 检查当前项是否被选中
      if (data.includes(item.id)) {
        selectKey.value.push(item)
        // 如果选中的项有子类别，将所有子类别也添加到选中列表中
        if (item.children && item.children.length > 0) {
          const getAllChildren = (children) => {
            children.forEach((child) => {
              selectKey.value.push(child)
              if (child.children && child.children.length > 0) {
                getAllChildren(child.children)
              }
            })
          }
          getAllChildren(item.children)
        }
      }
      // 继续递归检查子类别
      if (item.children && item.children.length > 0) {
        findSelectedCategories(item.children)
      }
    })
  }

  findSelectedCategories(listData.value)
  console.log(' selectKey -->', selectKey)
}
const handleSelect = () => {
  treePicker.value._show()
}
const getTreeData = async () => {
  const res = await useRequestWrap(() => httpGet('/study/portal/category/multi/level/list'))
  listData.value = res
}
const handleScreen = async () => {
  if (period.value === '') {
    toast.show('请输入目标学时')
    return
  }
  if (selectKey.value.length === 0) {
    toast.show('请选择课程')
    return
  }

  showLoading()
  const res = await useRequestWrap(
    () =>
      httpPost('/study/portal/course/makeup', {
        taskParams: { categoryIds: selectKey.value.map((i) => i.id), period: period.value },
      }),
    () => {
      hideLoading()
    },
  )
  if (!res.platformCourses.length) {
    toast.show('未找到符合当前学时的课程组合')
    return
  }
  courseList.value = res.platformCourses
  selectedInfo.period = res.period
  selectedInfo.amount = res.amount
  console.log(' res -->', res)
}
getTreeData()

//换一换
const handleChange = async (item) => {
  console.log(' item -->', item)

  // 排除当前所有课程
  let excludeCourseIds = courseList.value.map((course) => course.courseId)

  const res = await useRequestWrap(() =>
    httpPost('/study/portal/course/makeup', {
      excludeCourseIds,
      taskParams: {
        categoryIds: selectKey.value.map((i) => i.id),
        period: item.period, // 使用当前要替换课程的学时，而不是全局目标学时
      },
    }),
  )

  if (!res.success) {
    toast.show('未找到符合当前学时的课程组合')
    return
  }

  // 找到要替换的课程并替换
  const targetIndex = courseList.value.findIndex((course) => course.courseId === item.courseId)
  if (targetIndex !== -1) {
    courseList.value.splice(targetIndex, 1, ...res.platformCourses)
  }

  // 重新计算总学时和金额
  calculateTotal()
}

// 删除课程
const handleDelete = (item) => {
  const index = courseList.value.findIndex((course) => course.courseId === item.courseId)
  if (index !== -1) {
    courseList.value.splice(index, 1)
    // 重新计算总学时和金额
    calculateTotal()
  }
}

// 计算总学时和金额
const calculateTotal = () => {
  selectedInfo.period = courseList.value.reduce((total, course) => total + course.period, 0)
  selectedInfo.amount = courseList.value.reduce((total, course) => total + course.price, 0)
}

// 去购物车
const handleClick = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/user/shop/car/addCourses', {
      courseIds: courseList.value.map((i) => i.courseId).join(','),
    }),
  )
  toast.show('添加成功')
  courseList.value = []
}

// 去结算
const selectPayTypeVisible = ref(false)
const handlePay = () => {
  if (!courseList.value.length) {
    toast.show('未选择课程')
    return
  }
  if (selectPayType.includes(siteName)) {
    selectPayTypeVisible.value = true
    return
  }
  toPay()
}
const handleSelectTypeConfirm = (data) => {
  selectPayTypeVisible.value = false
  toPay(data)
}
const toPay = async (data) => {
  console.log(' data -->', data)
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/order/saveOrder', {
      courseIds: courseList.value.map((i) => i.courseId).join(','),
      planType: data,
    }),
  )
  toPage('/pages/payOrder/payOrder?id=' + res)
}
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  padding-left: 23rpx;
  border: 1px solid #dddddd;
  border-radius: 10rpx;
  &::after {
    display: none;
  }
}

.bg {
  background: url(../../static/course/znxk_bg.png) no-repeat;
  background-size: 100% 100%;
}
</style>
