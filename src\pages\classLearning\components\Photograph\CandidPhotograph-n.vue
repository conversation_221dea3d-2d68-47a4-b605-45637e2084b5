<template>
  <movable-area
    class="movable-area"
    style="position: fixed; left: 0; width: 750rpx; pointer-events: none"
    :style="{ height: windowHeight + 'px' }"
  >
    <movable-view direction="all" class="movable-view" style="width: 200rpx; height: 265rpx">
      <view class="box" style="pointer-events: none">
        <!-- #ifdef H5 -->
        <H5Camera ref="cameraCom"></H5Camera>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <MPCamera ref="cameraCom"></MPCamera>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <AppCamera ref="cameraCom" width="200rpx" height="265rpx"></AppCamera>
        <!-- #endif -->
        <view class="text-box">
          <text class="text">全程随机抓拍</text>
        </view>
      </view>
    </movable-view>
  </movable-area>
</template>

<script setup>
// classify：1拍照 2抓拍 3视频

import { httpUpload } from '@/utils/uniHttp'
import qs from 'qs'
import { useToast } from 'wot-design-uni'
import H5Camera from '../Camera/H5-Camera.vue'
import MPCamera from '../Camera/MP-Camera.vue'
import AppCamera from '../Camera/App-Camera.nvue'
import { defineEmits, defineExpose, defineOptions, defineProps, ref } from 'vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps(['cw'])
const emit = defineEmits(['verify'])
const toast = useToast()
const cameraCom = ref(null)
const systemInfo = uni.getSystemInfoSync()
const windowHeight = systemInfo.windowHeight // 可使用窗口高度

let isProcessing = false
// 拍照
const handleTakePhoto = async () => {
  if (isProcessing) return
  isProcessing = true
  let photo = await cameraCom.value.takePhoto()
  const params = {
    coursewareId: props.cw.resId,
    courseId: props.cw.courseId,
    lessonLocation: props.cw.lessonLocation,
    classify: 2,
  }
  const res = await httpUpload(
    `/study/portal/play/verify/saveRecordFile?${qs.stringify(params)}`,
    photo,
  )
  if (res.code !== 0) {
    emit('verify', false)
    toast.show(res.msg)
    isProcessing = false
    return
  }
  emit('verify', true, res.data)
  toast.show('验证成功')
}

defineExpose({
  takePhoto: handleTakePhoto,
})
</script>

<style lang="scss" scoped>
.candid-wrapper {
  position: fixed;
  //bottom: 250rpx;
  right: 0;
  width: 750rpx;
  pointer-events: none;
}

.box {
  position: relative;
  width: 200rpx !important;
  height: 265rpx !important;
  overflow: hidden;
  pointer-events: auto;
  background: #000;
  border-radius: 6rpx;

  .text-box {
    position: absolute;
    bottom: 15rpx;
    left: 0;
    width: 100%;
    color: #fff;
    text-align: center;
    letter-spacing: 2rpx;
    opacity: 0.9;

    .text {
      font-size: 18rpx;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
