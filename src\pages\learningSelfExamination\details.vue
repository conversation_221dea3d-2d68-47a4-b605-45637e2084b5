<route lang="json5">
{
  style: {
    navigationBarTitleText: '学员自查章节详情',
  },
}
</route>
<template>
  <view class="container px-20rpx py-27rpx !overflow-auto">
    <view style="background: linear-gradient(0deg, #fff, #fff, #fff4e9)" class="overflow-hidden">
      <view
        class="pt-38rpx pl-34rpx pr-20rpx pb-30rpx border-1rpx border-#eee border-b-solid mb-31rpx lh-30rpx"
      >
        <view class="flex items-start gap-17rpx mb-30rpx">
          <text class="text-#333333 text-28rpx font-bold lh-28rpx">{{ courseDetail.cname }}</text>
          <text
            v-if="courseDetail.studyStatus === '03' || courseDetail.studyStatus === '04'"
            class="tag-base text-#fff bg-[var(--primary-color)] flex-shrink-0"
          >
            已学完
          </text>
          <text
            v-if="courseDetail.studyStatus === '02'"
            class="tag-base text-#fff bg-#979996 flex-shrink-0"
          >
            学习中
          </text>
          <text
            v-if="courseDetail.studyStatus === '01'"
            class="tag-base text-#fff bg-#979996 flex-shrink-0"
          >
            未学习
          </text>
        </view>
        <view class="flex items-center justify-between text-26rpx">
          <view>
            <text>时长：</text>
            <text class="text-[var(--primary-color)]">
              {{ formatSeconds(courseDetail.cperiod) }}
            </text>
          </view>
          <view>
            <text>时长：</text>
            <text class="text-[var(--primary-color)]">{{ courseDetail.cperiod }}</text>
          </view>
        </view>

        <view class="text-26rpx mt-20rpx">
          <text>时间：</text>
          <text>{{ courseDetail.startTime || '--' }} 至 {{ courseDetail.finishTime || '--' }}</text>
        </view>
      </view>
      <view v-for="item in courseDetail.courseWareList" class="pl-22rpx pr-18rpx pb-31rpx">
        <view class="bg-#f1f4ff rounded-10rpx px-20rpx pb-31rpx pt-36rpx relative">
          <view class="absolute top-0 right-0">
            <text
              v-if="item.studyStatus === '03' || item.studyStatus === '04'"
              class="tag-base text-#fff bg-#5a81f4"
            >
              已学完
            </text>
            <text v-if="item.studyStatus === '02'" class="tag-base text-#fff bg-#979996">
              学习中
            </text>
            <text v-if="item.studyStatus === '01'" class="tag-base text-#fff bg-#979996">
              未学习
            </text>
          </view>
          <view class="text-22rpx text-#333 lh-22rpx">
            <view class="mb-29rpx">
              <view>{{ item.name }}</view>
            </view>

            <view class="text-#026FDA mb29rpx">时长：{{ formatSeconds(item.period) }}</view>

            <view class="mb-29rpx">
              <view class="grid grid-cols-2 text-#888888 mb-22rpx">
                <view>开始时间</view>
                <view>结束时间</view>
              </view>
              <view class="grid grid-cols-2 text-#333">
                <view>{{ item.startStudyTime || '--' }}</view>
                <view>{{ item.endStudyTime || '--' }}</view>
              </view>
            </view>
          </view>
          <scroll-view scroll-x>
            <view class="flex gap-24rpx">
              <view v-for="img in item.playPhotos" class="w-222rpx h-277rpx flex-shrink-0 relative">
                <view class="w-full h-full" @click="handlePreview(img)">
                  <KyImage :src="img.imageUrl" width="100%" height="100%"></KyImage>
                </view>
                <view
                  class="absolute bottom-0 left-0 w-full h-50rpx lh-50rpx text-center bg-#0a020480 text-20rpx text-#fff"
                >
                  {{ img.createTime }}
                </view>
              </view>
            </view>
          </scroll-view>
          <view
            v-if="
              finishOnce == '01' &&
              (creditStatus == '00' || creditStatus == '02') &&
              item.studyStatus != '01'
            "
            class="text-center mt-29rpx"
          >
            <wd-button
              plain
              class="!min-w-140rpx !h-52rpx !rounded-14rpx !bg-transparent !p0 !text-22rpx"
              @click="handleRestudy(item)"
            >
              立即重学
            </wd-button>
          </view>
        </view>
      </view>
    </view>

    <up-popup :show="previewVisible" mode="center" @close="previewVisible = false">
      <image
        :src="previewImgUrl"
        mode="widthFix"
        class="w-750rpx"
        @click="previewVisible = false"
      />
    </up-popup>
  </view>
</template>

<script setup>
import { formatSeconds } from '@/utils/formatSeconds'
import { httpGet } from '@/utils/uniHttp'
import KyImage from '@/components/KyImage.vue'
import { useMessage, useToast } from 'wot-design-uni'

const toast = useToast()
let creditStatus, finishOnce, courseId, uplanId
const courseDetail = ref({})
const getCourseDetails = async (courseId) => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/plan/inspection/course/detail', { courseId }),
  )
  courseDetail.value = res
}

// 立即重学
const message = useMessage()
const handleRestudy = async (item) => {
  message
    .confirm({
      msg: '确认重学吗？',
      title: '提示',
    })
    .then(async () => {
      await useRequestWrap(() =>
        httpGet('/study/portal/plan/courseware/restudy', {
          ucwId: item.ucwId,
          uplanId,
        }),
      )
      toast.show('重学成功')
      getCourseDetails(courseId)
    })
    .catch(() => {})
}

// 图片预览
const previewVisible = ref(false)
const previewImgUrl = ref('')
const handlePreview = (img) => {
  console.log('点击')
  previewImgUrl.value = img.imageUrl
  previewVisible.value = true
}

onLoad((options) => {
  finishOnce = options.finishOnce
  creditStatus = options.creditStatus
  courseId = options.courseId
  uplanId = options.uplanId
  getCourseDetails(courseId)
})
</script>

<style lang="scss" scoped>
.tag-base {
  padding: 7rpx 9rpx 7rpx 10rpx;
  font-size: 16rpx;
  line-height: 16rpx;
  border-radius: 0rpx 10rpx 0rpx 10rpx;
}
.finished {
  color: #fff;
  background: var(--primary-color);
}
</style>
