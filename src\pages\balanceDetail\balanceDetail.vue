<route lang="json5">
{
  style: {
    navigationBarTitleText: '余额明细',
  },
}
</route>
<template>
  <view class="container">
    <ky-scroll-view :apiFun="(params) => httpGet('/trade/portal/account/detail/page', params)">
      <template #default="{ data }">
        <view
          v-for="item in data"
          :key="item.id"
          class="m-20rpx py-33rpx px-32rpx rounded-20rpx bg-#fff first:mt-27rpx"
        >
          <view class="flex items-center justify-between">
            <text class="text-#333333 text-30rpx">{{ item.demo }}</text>
            <text
              class="font-bold text-30rpx text-#333333"
              :class="
                !(item.changetype == '0' || item.changetype === '2') &&
                '!text-[var(--primary-color)]'
              "
            >
              {{ item.changetype == '0' || item.changetype === '2' ? '-' : '+'
              }}{{ item.priceCredit }}元
            </text>
          </view>
          <view class="mt-18rpx">
            <text class="text-22rpx text-#888888">{{ item.occurrencetime }}</text>
          </view>
        </view>
      </template>
    </ky-scroll-view>
  </view>
</template>

<script setup>
import KyScrollView from '@/components/KyScrollView.vue'
import { httpGet } from '@/utils/uniHttp'
</script>

<style lang="scss" scoped></style>
