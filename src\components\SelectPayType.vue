<template>
  <div>
    <KyDialog v-model:visible="visible" title="购买前请设置培训类型" @confirm="handleConfirm">
      <wd-radio-group v-model="select" shape="dot">
        <wd-radio value="在岗培训">在岗培训</wd-radio>
        <wd-radio value="岗前培训">岗前培训</wd-radio>
      </wd-radio-group>
    </KyDialog>
  </div>
</template>

<script setup>
import KyDialog from './KyDialog.vue'
const visible = defineModel('visible')
const select = ref('在岗培训')
const emit = defineEmits(['confirm'])

const handleConfirm = () => {
  console.log(select)
  if (!select.value) {
    uni.showToast({
      title: '请选择培训类型',
      icon: 'none',
    })
  }
  emit('confirm', select.value)
}
</script>

<style lang="scss" scoped></style>
