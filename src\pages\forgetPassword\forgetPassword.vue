<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="container bg-#ffffff" :style="{ paddingTop: 'var(--status-bar-height)' }">
    <view class="h-44px flex items-center gap-10rpx mb-20rpx mt-10rpx ml23rpx">
      <!-- #ifdef H5 -->
      <SvgIcon icon="arrow-right" class="w-30rpx rotate-180" />
      <!-- #endif -->
      <!-- #ifndef H5 -->
      <image
        :src="getCdnImageUrl('/common/arrow_left_primary.png')"
        mode="widthFix"
        class="w-17rpx"
      />
      <!-- #endif -->
      <text class="text-28rpx lh-28rpx text-[var(--primary-color)]" @click="back">返回</text>
    </view>

    <view class="px65rpx">
      <view class="text-40rpx !text-#333333 font-bold mb-10rpx">忘记密码</view>
      <view class="text-24rpx text-#999999 mb-100rpx">
        若账号未绑定邮箱或手机，请联系客服
        <text class="text-[var(--primary-color)]" @click="call">{{ customerTelephone }}</text>
        找回密码
      </view>

      <view class="form">
        <!-- 账号 -->
        <view>
          <view class="label !text-#333333">手机号</view>
          <wd-input placeholder="请输入手机号" v-model="formData.identifier" />
        </view>
        <!-- 短信验证码 -->
        <view>
          <view class="label !text-#333333">短信验证码</view>
          <wd-input placeholder="请输入短信验证码" v-model="formData.captchaCode">
            <template #suffix>
              <wd-button size="small" @click="handleGetCode" :disabled="isSend">
                <text v-if="!isSend">获取验证码</text>
                <text v-else>{{ time }}秒后重新获取</text>
              </wd-button>
            </template>
          </wd-input>
        </view>
        <!-- 设置新密码 -->
        <view>
          <view class="label !text-#333333">设置新密码</view>
          <PasswordInput v-model:password="formData.newPassword" placeholder="请输入新密码" />
        </view>
        <!-- 确认新密码 -->
        <view>
          <view class="label !text-#333333">确认新密码</view>
          <PasswordInput v-model:password="formData.confirmPassword" placeholder="请确认新密码" />
        </view>
      </view>

      <wd-button
        class="mt60rpx !text-32rpx !w-full !h96rpx"
        :style="{
          boxShadow: `0 0 15px 0 ${getThemeColorWithOpacity()}`,
        }"
        :disabled="
          !formData.identifier ||
          !formData.captchaCode ||
          !formData.newPassword ||
          !formData.confirmPassword
        "
        :round="false"
        @click="handleUpdate"
      >
        确认修改
      </wd-button>
    </view>

    <!-- 验证码 -->
    <Captcha v-model:visible="captchaVisible" @success="handleSuccess" />
  </view>
</template>

<script setup>
import Captcha from '@/components/Captcha/Captcha.vue'
import PasswordInput from '@/components/PasswordInput.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { getThemeColorWithOpacity } from '@/utils/color'
import { getCdnImageUrl } from '@/utils/images'
import { phoneReg } from '@/utils/regex'
import { toPage } from '@/utils/toPage'
import { httpPost } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'
const formData = reactive({
  identifier: '',
  newPassword: '',
  captchaCode: '',
  confirmPassword: '',
})
const captchaVisible = ref(false)
const toast = useToast()
const back = () => {
  uni.navigateBack()
}
// 获取客服电话
const { customerTelephone } = useGetCustomerTelephone()
const call = () => {
  uni.makePhoneCall({
    phoneNumber: customerTelephone.value,
    success: () => {
      console.log('拨号成功')
    },
    fail: (err) => {
      console.log('拨号失败', err)
    },
  })
}

// 获取验证码
const handleGetCode = async () => {
  if (isSend.value) return
  if (!phoneReg.test(formData.identifier)) return toast.show('请输入合规的手机号')
  captchaVisible.value = true
}

let captchaVerification = ''
const isSend = ref(false)
const time = ref(60)
let timer = null
const sendCode = async () => {
  const res = await useRequestWrap(() =>
    httpPost('/admin-svc/user/captcha/check/sendCode?mobile=' + formData.identifier, {
      captchaVerification,
    }),
  )
  if (!res) return
  time.value = 60
  isSend.value = true
  timer = setInterval(() => {
    time.value--
    if (time.value <= 0) {
      clearInterval(timer)
      isSend.value = false
    }
  }, 1000)
}

const handleUpdate = async () => {
  if (!phoneReg.test(formData.identifier)) return toast.show('请输入合规的手机号')
  if (formData.newPassword !== formData.confirmPassword) return toast.show('两次输入密码不一致')

  const res = await useRequestWrap(() => httpPost('/admin-svc/user/resetPassword', formData))
  toast.success('修改成功')
  setTimeout(() => {
    toPage('/pages/login/login')
  }, 1000)
}

const handleSuccess = async (data) => {
  captchaVerification = data.captchaVerification
  sendCode()
}
</script>

<style lang="scss" scoped>
:deep(.form) {
  display: flex;
  flex-direction: column;
  gap: 44rpx;
  .label {
    display: flex;
    gap: 6rpx;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    line-height: 20rpx;
    color: #666666;
  }
  .wd-input {
    padding-bottom: 10rpx;
    background: transparent;
    &::after {
      background: #eeeeee;
    }

    .wd-input__icon {
      background: transparent;
    }
    input {
      font-size: 32rpx;
    }
    .uni-input-placeholder {
      font-size: 32rpx !important;
      color: #c7c7c7;
    }
  }

  .is-not-empty {
    &::after {
      background: #eeeeee !important;
    }
  }
}

:deep(.wd-checkbox__shape) {
  --wot-checkbox-size: 28rpx;
}

:deep(.wd-checkbox__label) {
  display: none;
}
</style>
