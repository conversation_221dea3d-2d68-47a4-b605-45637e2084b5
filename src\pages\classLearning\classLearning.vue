<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级学习',
  },
}
</route>
<template>
  <view class="container">
    <ky-video
      ref="kyVideoCom"
      class="w-full h-230px"
      :poster="poster"
      :courseList="courseList"
      :clearPlayInfo="clearPlayInfo"
      @ended="handleEnded"
    />
    <!-- 操作栏 -->
    <view class="flex justify-between items-center p-20rpx">
      <wd-button class="!m0" size="small" @click="handleStartLearn">立即学习</wd-button>
      <view class="text-26rpx text-#999 flex items-center gap-10rpx">
        <wd-switch v-model="checked" size="18" />
        <text>仅显示未完成章节</text>
      </view>
    </view>
    <!-- 章节列表 -->
    <view
      class="space-y-20rpx px-20rpx overflow-auto"
      :style="{ height: getViewHeight('230px', '49px', '10px') }"
    >
      <view
        v-for="(c, cIndex) in courseList"
        :key="cIndex"
        class="shadow-[0rpx_0rpx_20rpx_0rpx_rgba(0,0,0,0.09)] rounded-20rpx grid transition-all duration-300 bg-#fff"
        :class="c.isFold ? 'fold' : 'unfold'"
      >
        <!-- 章节信息 -->
        <view class="flex gap-20rpx p-20rpx overflow-hidden">
          <view
            class="text-20rpx text-#fff bg-[var(--primary-color)] rounded-5rpx px-12rpx py-5rpx h-fit flex-shrink-0"
          >
            第{{ cIndex + 1 }}门课
          </view>
          <view class="flex-1 overflow-hidden flex flex-col justify-between">
            <view class="text-28rpx font-bold mb-10rpx ellipsis">{{ c.cname }}</view>
            <view class="text-22rpx text-#999">
              共
              <text class="text-[var(--primary-color)]">
                {{ (c.cperiod / 3600).toFixed(2) }}
              </text>
              学时
            </view>
          </view>
          <wd-icon
            name="caret-down-small"
            size="26px"
            color="#999"
            class="h-fit translate-y-[-6rpx] scale-x-125 origin-center transition-transform duration-300 flex-shrink-0"
            :class="{ 'rotate-[-180deg]': c.isFold }"
            @click="c.isFold = !c.isFold"
          ></wd-icon>
        </view>
        <!-- 课件列表 -->
        <view class="border-2rpx border-t-solid border-[#E5E5E5] overflow-hidden">
          <template v-for="(cw, cwIndex) in c.courseWareList">
            <view
              v-if="checked ? cw.studyStatus !== '03' : true"
              class="flex gap-20rpx p-20rpx"
              @click="click_cw(cw, cIndex, cwIndex)"
            >
              <view class="tag">录播</view>
              <view class="flex-1">
                <view class="text-26rpx font-bold">{{ cw.name }}</view>
                <view class="text-22rpx text-#999 mt-10rpx flex-1">
                  <view class="text-20rpx flex items-center gap-20rpx relative">
                    <text>已播放 {{ getLearningProgress(cw) }}%</text>
                    <text>时长 {{ formatDateTime(cw.period) }}</text>
                    <view
                      v-if="playingCw[0] === cIndex && playingCw[1] === cwIndex"
                      class="tag absolute right-0"
                    >
                      当前观看
                    </view>
                  </view>
                  <wd-progress
                    class="!pb-0 !pt-10rpx"
                    hide-text
                    :percentage="getLearningProgress(cw)"
                    :color="themeStore.primaryColor"
                    :duration="0"
                  ></wd-progress>
                </view>
              </view>
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 是否继续学习剩余章节 -->
    <ky-dialog
      v-model:visible="learningVisible"
      title="是否继续学习剩余章节"
      @confirm="handleConfirm"
    ></ky-dialog>

    <!-- 学习提示 -->
    <NoticeBox
      v-model:visible="noticeVisible"
      title="验证照片采集，请确保为本人操作"
      confirmText="下一步"
      @confirm="handleNoticeConfirm"
      @close="handleNoticeClose"
    />
  </view>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import NoticeBox from '@/components/NoticeBox.vue'
import { useThemeStore } from '@/store'
import { formatDateTime } from '@/utils/formatDateTime'
import { getViewHeight } from '@/utils/getViewHeight'
import { httpGet } from '@/utils/uniHttp'
import KyVideo from './components/KyVideo/KyVideo.vue'
import dayjs from 'dayjs'

const themeStore = useThemeStore()
const { toast, showLoading, hideLoading } = useLoading()

const kyVideoCom = ref(null) //视频组件
const poster = ref('')
const courseList = ref([])
let id
const getClassDetails = async () => {
  const res = await useRequestWrap(() => httpGet('/study/portal/plan/classDetails', { planId: id }))
  res.forEach((item) => {
    item.isFold = false
    item.courseWareList.forEach((cw) => {
      cw.lessonLocation ?? (cw.lessonLocation = 0)
      cw.learningProgress = parseInt((cw.lessonLocation / cw.period) * 100)
    })
  })
  courseList.value = res
  checkNoticeTime()
}

// 操作栏
const checked = ref(true)
// 立即学习
const handleStartLearn = () => {
  const shouldLearnCwInfo = getCw()
  if (!shouldLearnCwInfo) return toast.show('已学完全部课程')
  const [cI, cwI] = playingCw.value
  const cw = courseList.value[cI]?.courseWareList[cwI]
  if (cw?.playFlag) return
  if (cw) {
    click_cw(cw, cI, cwI)
  } else {
    click_cw(shouldLearnCwInfo.courseware, shouldLearnCwInfo.cI, shouldLearnCwInfo.cwI)
  }
}

const playingCw = ref([-1, -1])
const click_cw = async (cw, cIndex, cwIndex) => {
  if (!uni.getStorageSync('lastNoticeTime')) {
    noticeVisible.value = true
    return
  }
  if (cw.studyStatus === '03') {
    toast.show('已完成章节无需重新学习')
    return
  }
  try {
    showLoading()
    const shouldLearnCwInfo = getCw()
    // 只能按照顺序学习
    if (cw.studyStatus !== '03') {
      if (cIndex !== shouldLearnCwInfo.cI || cwIndex !== shouldLearnCwInfo.cwI) {
        toast.show('请继续学习 ' + shouldLearnCwInfo.courseware.name)
        return
      }
    }
    playingCw.value = [cIndex, cwIndex]
    await kyVideoCom.value.play(cw)
    hideLoading()
  } catch (error) {
    playingCw.value = [-1, -1]
    hideLoading()
  }
}

// 计算学习进度的方法
const getLearningProgress = (item) => {
  if (item.period === null) return 0
  if (item.studyStatus === '03') {
    return 100
  }
  return Math.min(parseInt((item.lessonLocation / item.period) * 100), 100)
}

const handleEnded = async () => {
  await getClassDetails()
  learningVisible.value = true
}

// 是否继续学习剩余章节
const learningVisible = ref(false)
const handleConfirm = () => {
  const shouldLearnCwInfo = getCw()
  if (!shouldLearnCwInfo) {
    learningVisible.value = false
    return toast.show('已学完全部课程')
  }
  playingCw.value = [shouldLearnCwInfo.cI, shouldLearnCwInfo.cwI]
  kyVideoCom.value.play(shouldLearnCwInfo.courseware)
  learningVisible.value = false
}

// 获取需要学习的课件
const getCw = () => {
  let cI = 0
  let cwI = 0
  let result = null

  for (let i = cI; i < courseList.value.length; i++) {
    const course = courseList.value[i]
    for (let j = cwI; j < course.courseWareList.length; j++) {
      const courseware = course.courseWareList[j]
      if (!result && courseware.studyStatus === '01') result = { cI: i, cwI: j }
      if (courseware.studyStatus === '02') {
        result = { cI: i, cwI: j }
        break
      }
    }
  }

  if (!result) return null

  if (courseList.value.length) {
    result.courseware = courseList.value[result.cI].courseWareList[result.cwI]
  } else {
    result = {
      courseware: {},
      cI: -1,
      cwI: -1,
    }
  }
  return result
}

function clearPlayInfo() {
  playingCw.value = [-1, -1]
}

// 学习提示（一天弹出一次）
const noticeVisible = ref(false)
const checkNoticeTime = () => {
  const lastNoticeTime = uni.getStorageSync('lastNoticeTime')
  if (!lastNoticeTime || dayjs().diff(dayjs(lastNoticeTime), 'day') >= 1) {
    noticeVisible.value = true
  } else {
    learningVisible.value = true
  }
}

const handleNoticeConfirm = () => {
  noticeVisible.value = false
  uni.setStorageSync('lastNoticeTime', dayjs().format('YYYY-MM-DD HH:mm:ss'))
}
const handleNoticeClose = () => {
  uni.removeStorageSync('lastNoticeTime')
}

onHide(() => {
  const cw = courseList.value[playingCw.value[0]]?.courseWareList[playingCw.value[1]]
  if (!cw) return
  uni.navigateBack({
    delta: 1,
    success: function () {
      // 返回成功后显示弹框
      uni.showModal({
        title: '警告',
        content: '系统检测到您在学习中退至后台，已退出学习页面。请勿退出学习页面！',
        showCancel: false,
        success: (res) => {},
      })
    },
  })
})
onLoad(({ id: _id }) => {
  id = _id
  getClassDetails()
  poster.value = uni.getStorageSync('studyImage')
})
onUnload(() => {
  uni.removeStorageSync('studyImage')
})
</script>

<style lang="scss" scoped>
.fold {
  grid-template-rows: 114rpx 0fr;
}

.unfold {
  grid-template-rows: 114rpx 1fr;
}

.tag {
  height: fit-content;
  padding: 2rpx 12rpx;
  margin-top: 2rpx;
  font-size: 20rpx;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 5rpx;
}
</style>
