<template>
  <div ref="playerRef" class="player-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Player from 'xgplayer'

const props = defineProps({
  url: {
    type: String,
    required: true,
    default: 'https://newmedia.gdsjxjy.com/GDFKY2018/GDFKY20180602410601.mp4',
  },
  poster: {
    type: String,
    default: '',
  },
  autoplay: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['speed-change', 'play', 'pause'])
const playerRef = ref(null)
let player = null

onMounted(() => {
  initPlayer()
})

const initPlayer = () => {
  player = new Player({
    el: playerRef.value,
    url: props.url,
    poster: props.poster,
    autoplay: props.autoplay,
    fluid: true,
    playbackRate: [0.5, 0.75, 1, 1.25, 1.5, 2],
    // 开启倍速播放键
    playbackRateActive: true,

    // 自定义配置
    controls: {
      // 显示播放速率按钮
      playbackRate: true,
    },
  })

  // 监听事件
  player.on('playbackrateChange', (rate) => {
    console.log('播放速率改变:', rate)
    emit('speed-change', rate)
  })

  player.on('play', () => {
    emit('play')
  })

  player.on('pause', () => {
    emit('pause')
  })
}

// 提供一些控制方法
const playerMethods = {
  play: () => player?.play(),
  pause: () => player?.pause(),
  replay: () => player?.replay(),
  getCurrentTime: () => player?.currentTime,
  getDuration: () => player?.duration,
  getPlaybackRate: () => player?.playbackRate,
}

// 销毁播放器
onBeforeUnmount(() => {
  if (player) {
    player.destroy()
    player = null
  }
})

// 暴露方法给父组件
defineExpose(playerMethods)
</script>

<style scoped>
.player-container {
  width: 100%;
  height: 100%;
}
</style>
