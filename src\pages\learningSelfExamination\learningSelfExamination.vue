<route lang="json5">
{
  style: {
    navigationBarTitleText: '学员自查',
  },
}
</route>
<template>
  <view class="container px-20rpx py-27rpx !overflow-auto">
    <view class="bg-#fff rounded-20rpx text-30rpx overflow-hidden text-#333333 pb-27rpx">
      <view
        class="py-25rpx px-42rpx bg-[var(--primary-color)] w-fit text-#FFFFFF rounded-[20rpx_0rpx_30rpx_0rpx] font-bold"
      >
        学员培训详情
      </view>
      <view class="font-bold ml-32rpx mt-29rpx mb-39rpx">{{ planVo.planName }}</view>
      <view class="text-24rpx ml-32rpx my-35rpx">
        <text class="text-#888888">学员名称：</text>
        <text>{{ planVo.realName }}【身份证:{{ planVo.idCard }}】</text>
      </view>
      <view class="text-24rpx ml-32rpx my-35rpx">
        <text class="text-#888888">报名时间：</text>
        <text>{{ planVo.createTime }}(至{{ planVo.endTime }}过期)</text>
      </view>
      <view class="text-24rpx ml-32rpx mt-35rpx mb-71rpx">
        <text class="text-#888888">授课方式：</text>
        <text>{{ planVo.learnMode }}</text>
      </view>

      <view class="pl-12rpx mb-35rpx">
        <view class="title">班级完成情况</view>
        <view class="text-26rpx ml-15rpx lh-22rpx flex justify-between pr30rpx">
          <text>学习状态：{{ STUDY_STATUS[planVo.status] }}</text>
          <text>考试状态：{{ EXAM_STATUS[planVo.examStatus] }}</text>
        </view>
      </view>

      <view class="pl-12rpx pr-21rpx">
        <view class="title">课程完成情况</view>
        <view
          class="text-26rpx ml-2rpx bg-#f1f4ff rounded-2rpx flex justify-between pt-28rpx pb-30rpx px-21rpx"
        >
          <view class="flex flex-col items-center">
            <text class="text-#888888 text-23rpx">总学时</text>
            <text class="text-42rpx font-bold">{{ planVo.creditNum }}</text>
          </view>
          <view class="flex flex-col items-center">
            <text class="text-#888888 text-23rpx">已完成学时</text>
            <text class="text-42rpx font-bold text-[var(--primary-color)]">
              {{ planVo.totalCredit }}
            </text>
          </view>
          <view class="flex flex-col items-center">
            <text class="text-#888888 text-23rpx">章节总数</text>
            <text class="text-42rpx font-bold">{{ planVo.coursewareCount }}</text>
          </view>
          <view class="flex flex-col items-center">
            <text class="text-#888888 text-23rpx">已观看章节数</text>
            <text class="text-42rpx font-bold text-#026FDA">{{ planVo.studyCwCount }}</text>
          </view>
        </view>
      </view>
      <view class="flex justify-between text-24rpx text-#888 px-42rpx mt-21rpx">
        <text>开始时间：{{ planVo.studyStartTime || '--' }}</text>
        <text>完成时间：{{ planVo.studyEndTime || '--' }}</text>
      </view>
    </view>

    <view>
      <view class="title my-40rpx ml-12rpx">学习情况</view>

      <view
        class="rounded-20rpx overflow-hidden mb20rpx"
        style="background: linear-gradient(0deg, #fff, #fff, #fff4e9)"
        v-for="item in courseList"
      >
        <view class="pt-38rpx pl-34rpx pr-20rpx pb-30rpx border-1rpx border-#eee border-b-solid">
          <view class="flex items-start gap-17rpx mb-30rpx">
            <text class="text-#333333 text-28rpx font-bold lh-28rpx">{{ item.cname }}</text>
            <text
              v-if="item.studyStatus === '03' || item.studyStatus === '04'"
              class="tag-base text-#fff bg-[var(--primary-color)] flex-shrink-0"
            >
              已学完
            </text>
            <text
              v-if="item.studyStatus === '02'"
              class="tag-base text-#fff bg-#979996 flex-shrink-0"
            >
              学习中
            </text>
            <text
              v-if="item.studyStatus === '01'"
              class="tag-base text-#fff bg-#979996 flex-shrink-0"
            >
              未学习
            </text>
          </view>
          <view class="flex items-center justify-between text-26rpx">
            <view>
              <text>时长：</text>
              <text class="text-[var(--primary-color)]">
                {{ formatSeconds(item.cperiod) }}
              </text>
            </view>
            <view>
              <text>学时：</text>
              <text class="text-[var(--primary-color)]">{{ item.period }}</text>
            </view>
          </view>

          <view class="text-26rpx mt-20rpx">
            <text>时间：</text>
            <text>{{ item.startTime || '--' }} 至 {{ item.finishTime || '--' }}</text>
          </view>
        </view>

        <view class="text-center my-30rpx">
          <wd-button
            class="!w-400rpx !h-84rpx"
            @click="
              toPage('/pages/learningSelfExamination/details', {
                courseId: item.courseId,
                finishOnce: planVo.finishOnce,
                creditStatus: planVo.creditStatus,
                uplanId,
              })
            "
          >
            查看详情
          </wd-button>
        </view>
      </view>
    </view>
  </view>
  <image
    :src="getCdnImageUrl('/course/examinationBg.png')"
    mode="widthFix"
    class="absolute top-[-140px] left-23px w-full"
  />
</template>

<script setup>
import { EXAM_STATUS, STUDY_STATUS } from '@/utils/dataStatus'
import { formatSeconds } from '@/utils/formatSeconds'
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

let uplanId
const planVo = ref({})
const courseList = ref([])
const getCourseList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/plan/inspection/detail/course/list', { uplanId }),
  )
  planVo.value = res.planVo
  console.log('planVo -->', planVo)
  courseList.value = res.courseList
}

onLoad(({ id }) => {
  uplanId = id
  getCourseList()
})
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  z-index: 1;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 21rpx;
    font-size: 26rpx;
    color: var(--primary-color);
    &::before {
      width: 6rpx;
      height: 26rpx;
      margin-right: 14rpx;
      content: '';
      background: var(--primary-color);
    }
  }
  .tag-base {
    padding: 7rpx 9rpx 7rpx 10rpx;
    font-size: 16rpx;
    line-height: 16rpx;
    border-radius: 0rpx 10rpx 0rpx 10rpx;
  }
}
</style>
