<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级学习',
  },
}
</route>
<template>
  <view class="container !overflow-auto">
    <view style="position: sticky; top: 0; z-index: 10; background: #fff">
      <ky-video
        ref="kyVideoCom"
        style="width: 100%; height: 230px"
        class="w-full h-230px"
        :poster="poster"
        :courseList="courseList"
        :clearPlatInfo="clearPlatInfo"
        @ended="handleEnded"
      />
      <!-- 操作栏 -->
      <view class="toolbar">
        <view class="">
          <up-button
            text="立即学习"
            shape="circle"
            type="primary"
            size="mini"
            :color="primaryColor"
            @click="handleStartLearn"
          ></up-button>
        </view>
        <view class="right">
          <up-switch v-model="checked" size="18" :activeColor="primaryColor"></up-switch>
          <text class="text">仅显示未完成章节</text>
        </view>
      </view>
    </view>

    <!-- 章节列表 -->
    <view class="list">
      <view v-for="(c, cIndex) in courseList" :key="cIndex" class="item">
        <!-- 章节信息 -->
        <view class="info">
          <view class="left" :style="{ backgroundColor: primaryColor }">
            <text class="text">第{{ cIndex + 1 }}门课</text>
          </view>
          <view class="right">
            <text class="title ellipsis">{{ c.cname }}</text>
            <view class="period">
              <text class="text">共</text>
              <text class="text" :style="{ color: primaryColor, margin: '0 2rpx' }">
                {{ (c.cperiod / 3600).toFixed(2) }}
              </text>
              <text class="text">学时</text>
            </view>
          </view>
          <view>
            <up-icon
              name="arrow-down-fill"
              color="#999999"
              size="26rpx"
              @click="c.isFold = !c.isFold"
              :style="{
                transform: c.isFold ? `rotate(-180deg)` : `rotate(0deg)`,
                transition: '0.3s',
              }"
            ></up-icon>
          </view>
        </view>
        <!-- 课件列表 -->
        <view v-if="!c.isFold" class="course">
          <template v-for="(cw, cwIndex) in c.courseWareList">
            <view
              v-if="checked ? cw.studyStatus !== '03' : true"
              class="wrap"
              @click="click_cw(cw, cIndex, cwIndex)"
            >
              <view>
                <text class="tag" :style="{ color: primaryColor, borderColor: primaryColor }">
                  录播
                </text>
              </view>
              <view class="right">
                <view class="title">
                  <text class="text">{{ cw.name }}</text>
                </view>
                <view class="progress">
                  <view class="top">
                    <text class="text" style="marginright: 20rpx">
                      已播放 {{ cw.learningProgress }}%
                    </text>
                    <text class="text">时长 {{ formatDateTime(cw.period) }}</text>
                    <view
                      v-if="playingCw[0] === cIndex && playingCw[1] === cwIndex"
                      class="current absolute right-0"
                    >
                      <text
                        class="text"
                        :style="{ color: primaryColor, borderColor: primaryColor }"
                      >
                        当前观看
                      </text>
                    </view>
                  </view>
                  <view style="margintop: 10rpx">
                    <up-line-progress
                      :percentage="cw.learningProgress"
                      :activeColor="primaryColor"
                      :showText="false"
                      height="4"
                    ></up-line-progress>
                  </view>
                </view>
              </view>
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 是否继续学习剩余章节 -->
    <ky-dialog
      v-model:visible="learningVisible"
      title="是否继续学习剩余章节"
      @confirm="handleConfirm"
    ></ky-dialog>

    <up-loading-page
      :loading="loading"
      bg-color="#00000080"
      :loadingColor="primaryColor"
      :color="primaryColor"
    ></up-loading-page>
  </view>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'
import KyVideo from './components/KyVideo/KyVideo-n.nvue'
import dayjs from 'dayjs'
import { ref } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { useThemeStore } from '@/store'
import { formatDateTime } from '@/utils/formatDateTime'
import KyDialog from '@/components/KyDialog.vue'

const { primaryColor } = useThemeStore()

const loading = ref(false)
const kyVideoCom = ref(null) //视频组件
const poster = ref('')
const courseList = ref([])
let id
const getClassDetails = async () => {
  const res = await useRequestWrap(() => httpGet('/study/portal/plan/classDetails', { planId: id }))
  res.forEach((item) => {
    item.isFold = false
    item.courseWareList.forEach((cw) => {
      cw.lessonLocation ?? (cw.lessonLocation = 0)
      cw.learningProgress = parseInt((cw.lessonLocation / cw.period) * 100)
    })
  })
  courseList.value = res
}

// 操作栏
const checked = ref(true)
// 立即学习
const handleStartLearn = () => {
  const shouldLearnCwInfo = getCw()
  if (!shouldLearnCwInfo) {
    uni.showToast({
      icon: 'none',
      title: '已学完全部课程',
    })
    return
  }
  const [cI, cwI] = playingCw.value
  const cw = courseList.value[cI]?.courseWareList[cwI]
  if (cw?.playFlag) return
  if (cw) {
    click_cw(cw, cI, cwI)
  } else {
    click_cw(shouldLearnCwInfo.courseware, shouldLearnCwInfo.cI, shouldLearnCwInfo.cwI)
  }
}

const playingCw = ref([-1, -1])
const click_cw = async (cw, cIndex, cwIndex) => {
  // if (!uni.getStorageSync('lastNoticeTime')) {
  //   noticeVisible.value = true
  //   return
  // }
  if (cw.studyStatus === '03') {
    uni.showToast({
      icon: 'none',
      title: '已完成章节无需重新学习',
    })
    return
  }
  try {
    loading.value = true
    const shouldLearnCwInfo = getCw()
    // 只能按照顺序学习
    if (cw.studyStatus !== '03') {
      if (cIndex !== shouldLearnCwInfo.cI || cwIndex !== shouldLearnCwInfo.cwI) {
        uni.showToast({
          icon: 'none',
          title: '请继续学习 ' + shouldLearnCwInfo.courseware.name,
        })
        loading.value = false
        return
      }
    }
    playingCw.value = [cIndex, cwIndex]
    await kyVideoCom.value.play(cw)
    loading.value = false
  } catch (error) {
    playingCw.value = [-1, -1]
    loading.value = false
  }
}

const handleEnded = async () => {
  await getClassDetails()
  learningVisible.value = true
}

// 是否继续学习剩余章节
const learningVisible = ref(false)
const handleConfirm = () => {
  const shouldLearnCwInfo = getCw()
  if (!shouldLearnCwInfo) {
    learningVisible.value = false
    uni.showToast({
      icon: 'none',
      title: '已学完全部课程',
    })
    return
  }
  playingCw.value = [shouldLearnCwInfo.cI, shouldLearnCwInfo.cwI]
  kyVideoCom.value.play(shouldLearnCwInfo.courseware)
  learningVisible.value = false
}

// 获取需要学习的课件
const getCw = () => {
  let cI = 0
  let cwI = 0
  let result = null

  for (let i = cI; i < courseList.value.length; i++) {
    const course = courseList.value[i]
    for (let j = cwI; j < course.courseWareList.length; j++) {
      const courseware = course.courseWareList[j]
      if (!result && courseware.studyStatus === '01') result = { cI: i, cwI: j }
      if (courseware.studyStatus === '02') {
        result = { cI: i, cwI: j }
        break
      }
    }
  }

  if (!result) return null

  if (courseList.value.length) {
    result.courseware = courseList.value[result.cI].courseWareList[result.cwI]
  } else {
    result = {
      courseware: {},
      cI: -1,
      cwI: -1,
    }
  }
  return result
}

function clearPlatInfo() {
  playingCw.value = [-1, -1]
}

// 学习提示（一天弹出一次）
const noticeVisible = ref(false)
const checkNoticeTime = () => {
  const lastNoticeTime = uni.getStorageSync('lastNoticeTime')
  if (!lastNoticeTime || dayjs().diff(dayjs(lastNoticeTime), 'day') >= 1) {
    noticeVisible.value = true
  }
}

const handleNoticeConfirm = () => {
  noticeVisible.value = false
  uni.setStorageSync('lastNoticeTime', dayjs().format('YYYY-MM-DD HH:mm:ss'))
}
const handleNoticeClose = () => {
  uni.removeStorageSync('lastNoticeTime')
}

onLoad(({ id: _id }) => {
  id = _id
  getClassDetails()
  poster.value = uni.getStorageSync('studyImage')
  checkNoticeTime()
})
onUnload(() => {
  uni.removeStorageSync('studyImage')
})
</script>

<style lang="scss" scoped>
.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  flex-direction: row;
  background: #fff;

  .right {
    display: flex;
    flex-direction: row;
    align-items: center;

    .text {
      color: #999;
      font-size: 26rpx;
      margin-left: 10rpx;
    }
  }
}

.list {
  padding: 0 20rpx;
  margin-top: 20rpx;
  overflow: auto;

  .item {
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.09);
    border-radius: 20rpx;
    background: #fff;
    margin-bottom: 20rpx;

    .info {
      //flex gap-20rpx p-20rpx overflow-hidden
      display: flex;
      padding: 20rpx;
      overflow: hidden;
      flex-direction: row;
      align-items: start;

      .left {
        border-radius: 5rpx;
        padding: 5rpx 12rpx;
        margin-right: 12rpx;
        flex-shrink: 0;
        height: 36rpx;
        font-size: 24rpx;

        .text {
          color: #fff;
          font-size: 20rpx;
        }
      }

      .right {
        //flex-1 overflow-hidden flex flex-col justify-between
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          //text-28rpx font-bold mb-10rpx
          font-size: 28rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .period {
          display: flex;
          flex-direction: row;

          .text {
            //text-22rpx text-#999
            font-size: 22rpx;
            color: #999;
          }
        }
      }
    }

    .course {
      border-top: 2rpx solid #e5e5e5;
      overflow: hidden;

      .wrap {
        //flex gap-20rpx p-20rpx
        display: flex;
        padding: 20rpx;
        flex-direction: row;

        .tag {
          padding: 4rpx;
          font-size: 18rpx;
          color: #888888;
          border: 1rpx solid #cccccc;
          border-radius: 3rpx;
          margin-right: 20rpx;
        }

        .right {
          flex: 1;

          .title {
            .text {
              font-size: 26rpx;
              font-weight: bold;
            }
          }

          .progress {
            font-size: 22rpx;
            color: #999;
            margin-top: 10rpx;
            flex: 1;

            .top {
              //text-20rpx flex items-center gap-20rpx relative
              display: flex;
              align-items: center;
              position: relative;
              flex-direction: row;

              .text {
                font-size: 20rpx;
                color: #999;
              }

              .current {
                position: absolute;
                right: 0;

                .text {
                  padding: 4rpx;
                  font-size: 18rpx;
                  color: #888888;
                  border: 1rpx solid #cccccc;
                  border-radius: 3rpx;
                  margin-right: 20rpx;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
