<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级详情',
  },
}
</route>
<template>
  <view class="container p-20rpx !overflow-auto">
    <view class="info bg-#fff rounded-20rpx p-20rpx space-y-10rpx">
      <view class="text-#333333 font-bold">{{ classDetails.name }}</view>
      <view class="flex justify-between text-#666666 text-26rpx">
        <view>
          <text>培训学时：</text>
          <text class="text-[var(--primary-color)]">
            {{ classDetails.bxCredit + classDetails.xxCredit || '-' }} 课时
          </text>
        </view>
        <view>
          <text>培训费用：</text>
          <text class="text-[var(--primary-color)]">
            {{ classDetails.totalPrice?.toFixed(2) || '-' }} 元
          </text>
        </view>
      </view>
      <view class="text-#666666 text-26rpx">
        <text>培训时间：</text>
        <text
          v-if="classDetails.startTime && classDetails.endTime"
          class="text-[var(--primary-color)]"
        >
          {{ classDetails.startTime?.split(' ')[0] || '-' }} 至
          {{ classDetails.endTime?.split(' ')[0] || '-' }}
          <text v-if="!dayjs().isBefore(classDetails.endTime)">【班级已结束】</text>
        </text>
        <text v-else class="text-[var(--primary-color)]">待开班</text>
      </view>
      <ky-text-ellipsis
        :content="classDetails.description"
        :rows="3"
        class="text-#666 text-26rpx"
      ></ky-text-ellipsis>
      <EnrollmentManager :item="classDetails" class="!mt-30rpx !mb-10rpx">
        <wd-button v-if="classDetails.status === '10'" @click="handleEnterStudy">
          进入学习
        </wd-button>
      </EnrollmentManager>
    </view>

    <view class="course">
      <view class="title">
        <text>全部</text>
        <text>课程</text>
      </view>

      <view v-for="item in classDetails.courseList" :key="item.id" class="item">
        <ky-image :src="item.cimage" width="100%" height="200rpx" rounded="10rpx 0 0 10rpx" />
        <view class="content">
          <view class="ellipsis text-26rpx font-bold">{{ item.cname }}</view>
          <view class="flex flex-col items-start gap8rpx">
            <view class="tag">讲师：{{ item.teacherNames }}</view>
            <view class="tag">科目：{{ item.topCategoryName }}-{{ item.categoryName }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 报名信息 -->
    <ky-dialog
      v-model:visible="hintVisible"
      title="温馨提示"
      :show-cancel-button="false"
      @confirm="hintVisible = false"
    >
      <view class="text-left text-#000 lh-50rpx my-20rpx">
        本期班级将于
        {{ classDetails.endTime?.split(' ')[0] }}
        截止，请未完成的学员在班级结束前尽快完成，并因为拍照不规范造成的重学预留学习时间。
      </view>
    </ky-dialog>
    <!-- 免冠照上传 -->
    <Edit
      ref="editCom"
      :edit-field="{
        field: 'officialPhotoUrl',
        title: '免冠半身照',
        value: '',
      }"
    />
    <!-- 实名认证 -->
    <ky-dialog
      v-model:visible="realinfoVisible"
      title="温馨提示"
      confirmText="前往"
      @confirm="toPage('/pages/realInfo/realInfo')"
    >
      <view class="text-#000 lh-50rpx my-20rpx">开始学习前，请先完成实名认证</view>
    </ky-dialog>
    <!-- 微信小程序未实名弹框 -->
    <ky-dialog
      v-model:visible="wxRealinfoVisible"
      title="温馨提示"
      :show-cancel-button="false"
      @confirm="wxRealinfoVisible = false"
    >
      <view class="text-#000 lh-50rpx my-20rpx">请前往APP或者PC端进行实名认证</view>
    </ky-dialog>
    <!-- 浏览器校验 -->
    <ky-dialog
      v-model:visible="browserVisible"
      title="温馨提示"
      :show-cancel-button="false"
      @confirm="browserVisible = false"
    >
      <view class="text-#000 lh-50rpx my-20rpx">请前往微信内打开链接进行学习</view>
    </ky-dialog>
  </view>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import KyImage from '@/components/KyImage.vue'
import KyTextEllipsis from '@/components/KyTextEllipsis.vue'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { useUserStore } from '@/store'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import dayjs from 'dayjs'
import Edit from '../myInfo/components/Edit.vue'
import EnrollmentManager from '@/components/EnrollmentManager.vue'
import { isChromeBrowser, isWeixinBrowser, isWorkWeixinBrowser } from '@/utils/browser'

const userStore = useUserStore()
const { showLoading, hideLoading } = useLoading()

const classDetails = ref({})
const getClassDetails = async (id) => {
  showLoading()
  const res = await useRequestWrap(
    () => httpGet('/study/portal/plan/detail', { id }),
    () => hideLoading(),
  )
  classDetails.value = res
  if (res.endTime && dayjs().isBefore(dayjs(res.endTime))) {
    nextTick(() => {
      hintVisible.value = true
    })
  }
}

const hintVisible = ref(false)
const wxRealinfoVisible = ref(false)
const browserVisible = ref(false)
const isEnv = import.meta.env.MODE === 'development'
// 进入学习
const editCom = ref(null)
const handleEnterStudy = async () => {
  // 未上传免冠照
  if (!userStore.userInfo.officialPhoto) {
    editCom.value.open()
    return
  }
  // 未完成实名认证
  const realInfo = await userStore.getRealInfo()
  // 浏览器校验
  // #ifdef H5
  if (!isEnv) {
    if (!(isWeixinBrowser() || isWorkWeixinBrowser() || isChromeBrowser())) {
      browserVisible.value = true
      return
    }
  }
  // #endif
  if (!realInfo) {
    // #ifdef MP-WEIXIN
    wxRealinfoVisible.value = true
    // #endif
    // #ifndef MP-WEIXIN
    realinfoVisible.value = true
    // #endif
    return
  }
  uni.setStorageSync('studyImage', classDetails.value.imgPath)
  toPage('/pages/classLearning/classLearning', {
    id: classDetails.value.id,
  })
}
const realinfoVisible = ref(false)

onLoad(({ id }) => {
  getClassDetails(id)
})
</script>

<style lang="scss" scoped>
.course {
  margin-top: 30rpx;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 28rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: var(--primary-color);

    & > text:last-child {
      color: #333;
    }

    &::before {
      width: 8rpx;
      height: 30rpx;
      margin-right: 12rpx;
      content: '';
      background: var(--primary-color);
      border-radius: 3rpx;
    }
  }
  .item {
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(2, 50%);
    height: 190rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    background-color: #fff;
    border-radius: 10rpx;

    .content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 19rpx;
      .tag {
        padding: 2rpx 4rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: #b6b6b6;
        border: 1px solid #cccccc;
        border-radius: 3px;
      }
    }
  }
}
</style>
