<script setup lang="ts">
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { useAppStore } from './store'

const isDev = import.meta.env.DEV
const appStore = useAppStore()
appStore.init()

if (!isDev) {
  const systemInfo = uni.getSystemInfoSync()
  const windowWidth = systemInfo.windowWidth
  if (windowWidth > 500) {
    // window.location.href = 'https://www.gdsjxjy.com/'
  }
}

onLaunch(() => {})
onShow(() => {})
onHide(() => {})
</script>

<style lang="scss">
// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.tag {
  padding: 4rpx;
  font-size: 18rpx;
  color: #888888;
  border: 1rpx solid #cccccc;
  border-radius: 3rpx;
}

// 添加容器高度
.container {
  box-sizing: border-box;
  width: 750rpx;
  height: calc(var(--window-height) + var(--other-height));
  overflow: hidden;
}
</style>
