// 获取环境变量中的CDN基础路径
const CDN_URL = import.meta.env.VITE_CDN_URL

/**
 * 获取CDN图片完整路径
 * @param path 图片相对路径
 * @returns CDN完整路径
 */
export const getCdnImageUrl = (path: string): string => {
  // 如果是完整的http(s)链接，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }

  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${CDN_URL}${normalizedPath}`
}

/**
 * 批量获取CDN图片路径
 * @param paths 图片相对路径数组
 * @returns CDN完整路径数组
 */
export const getCdnImageUrls = (paths: string[]): string[] => {
  return paths.map((path) => getCdnImageUrl(path))
}
