interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

export const useRequestWrap = async <T = any>(
  request: () => Promise<ApiResponse<T>>,
  callback?: (res: ApiResponse<T>) => void,
): Promise<T> => {
  return new Promise((resolve, reject) => {
    request()
      .then((res) => {
        const { code, data, msg } = res
        if (code !== 0) {
          if (msg?.length > 20) {
            uni.showModal({
              title: '提示',
              content: msg,
              confirmText: '确定',
              showCancel: false,
            })
          } else {
            uni.showToast({
              title: msg,
              icon: 'none',
            })
          }

          reject(res)
        }
        callback?.(res)
        resolve(data)
      })
      .catch((err) => {
        callback?.(err)
        throw err
      })
  })
}
