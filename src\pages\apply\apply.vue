<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请证书',
  },
}
</route>

<template>
  <div class="pt-20rpx container grid grid-rows-[auto_1fr_auto]">
    <div class="bg-#fff px-20rpx py-10rpx space-y-10rpx">
      <div class="text-26rpx text-#222 flex">
        <div class="text-#666 w-130rpx flex justify-between">
          <span>姓</span>
          <span>名：</span>
        </div>
        <span>{{ userStore.userInfo.realName }}</span>
      </div>
      <div class="text-26rpx text-#222">
        <span class="text-#666">身份证号：</span>
        <span>{{ userStore.userInfo.idCard }}</span>
      </div>
      <div class="text-26rpx text-#222">
        <span class="text-#666">工作单位：</span>
        <span>{{ userStore.userInfo.orgName }}</span>
      </div>
      <div class="text-26rpx text-#222 flex">
        <div class="text-#666 w-130rpx flex justify-between">
          <span>职</span>
          <span>务：</span>
        </div>
        <span>{{ userStore.userInfo.duties }}</span>
      </div>
    </div>
    <z-paging ref="pagingRef" v-model="dataList" @query="queryList" :fixed="false">
      <view
        v-for="item in dataList"
        class="item flex items-center my20rpx h-150rpx bg-#fff p-20rpx"
      >
        <wd-checkbox
          v-model="item.checked"
          :checked-color="themeStore.primaryColor"
          shape="square"
          size="large"
        ></wd-checkbox>
        <ky-image
          :src="item.cimage"
          width="250rpx"
          height="100%"
          rounded="10rpx"
          class="flex-shrink-0"
          @click="item.checked = !item.checked"
        ></ky-image>
        <view class="flex flex-col justify-between h-full flex-1 ml-20rpx e">
          <view class="ellipsis-3">{{ item.cname }}</view>
          <view class="flex justify-between items-center">
            <view class="text-22rpx text-#999">
              <text>{{ item.topCategoryName }}</text>
              <text class="mx-10rpx">|</text>
              <text>{{ item.period }}学时</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <!-- 底部结算 -->
    <view
      class="bg-#f4f7fa flex items-center justify-between shadow-[0_-2px_10px_rgba(0,0,0,0.1)] pl20rpx"
    >
      <view class="flex items-center gap20rpx">
        <wd-checkbox
          v-model="checkedAll"
          :checked-color="themeStore.primaryColor"
          shape="square"
          size="large"
          @change="handleCheckedAll"
        >
          已选({{ selectedCourse.length }})
        </wd-checkbox>
        <view class="text-#666 text-26rpx">
          合计
          <span class="text-32rpx font-bold text-#ff5c0f">
            {{ selectedCourse.reduce((total, item) => total + item.period, 0) }}
          </span>
          学时
        </view>
      </view>
      <wd-button
        :round="false"
        class="!h-58px !m-0 !rounded-0"
        :disabled="selectedCourse.length === 0"
        @click="handleApply"
      >
        确定申请
      </wd-button>
    </view>
  </div>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import { useThemeStore, useUserStore } from '@/store'
import { httpGet } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'

const userStore = useUserStore()
const themeStore = useThemeStore()
const toast = useToast()

const dataList = ref([])
const pagingRef = ref(null)
const queryList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/archive/certificate/applyCourseList'),
  )
  pagingRef.value?.complete(res)
}

// 全选
const checkedAll = ref(false)
const selectedCourse = computed(() => {
  const arr = dataList.value.filter((item) => item.checked)
  if (arr.length < dataList.value.length || arr.length === 0) {
    checkedAll.value = false
  } else {
    checkedAll.value = true
  }
  return arr
})
const handleCheckedAll = () => {
  dataList.value.forEach((item) => {
    item.checked = checkedAll.value
  })
}
const handleApply = async () => {
  if (!userStore.userInfo.realName) {
    toast.show('请完善姓名信息')
    return
  }
  if (!userStore.userInfo.duties) {
    toast.show('请完善职务信息')
    return
  }
  if (!userStore.userInfo.orgName) {
    toast.show('请完善工作单位信息')
    return
  }

  await useRequestWrap(() =>
    httpGet('/study/portal/archive/applyByYearAndCourseIds', {
      courseIds: selectedCourse.value.map((item) => item.id).join(','),
      year,
    }),
  )
  toast.show('申请成功')
  toPage('/pages/study/study')
}

let year = ''
onLoad((option) => {
  year = option.year
})
</script>
