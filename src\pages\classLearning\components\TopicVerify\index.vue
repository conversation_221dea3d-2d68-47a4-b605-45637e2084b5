<template>
  <view>
    <Ky-dialog
      v-model:visible="visible"
      class="dialog"
      :showCancelButton="false"
      @closed="handleClosed"
      @opened="handleOpened"
      @confirm="handleConfirm"
    >
      <view class="text-38rpx text-#fb362e font-bold">请选择答案</view>
      <img :src="topicImg" alt="" class="mt-40rpx mb-10rpx" />
      <wd-radio-group v-model="result" shape="dot" inline class="flex gap-30rpx pb-20rpx">
        <wd-radio :value="answer">{{ answer }}</wd-radio>
        <wd-radio :value="answer + 5">{{ answer + 5 }}</wd-radio>
      </wd-radio-group>
    </Ky-dialog>
  </view>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import { httpGet } from '@/utils/uniHttp'
import qs from 'qs'

const { toast, showLoading, hideLoading } = useLoading()
const emit = defineEmits(['verify', 'opened'])
const visible = defineModel('visible')
const props = defineProps({
  cw: {
    type: Object,
    default: () => {},
  },
})

const topicImg = ref('')
const answer = ref('')
const result = ref('')
const handleOpened = async () => {
  emit('opened')
  showLoading()
  const res = await useRequestWrap(() =>
    httpGet(`/study/portal/play/formula/captcha?coursewareId=${props.cw.resId}`),
  )
  answer.value = res.answer
  topicImg.value = res.imageBase64
  result.value = res.answer
  hideLoading()
}
const handleClosed = () => {}

const handleConfirm = async () => {
  const params = {
    userAnswer: result.value,
    courseId: props.cw.courseId,
    coursewareId: props.cw.resId,
  }

  const res = await useRequestWrap(() =>
    httpGet(`/study/portal/play/formula/captcha/check?${qs.stringify(params)}`),
  )
  if (!res) {
    uni.showToast({
      title: '回答错误！请重新回答',
      icon: 'none',
    })
    handleOpened()
    return
  }

  visible.value = false
  emit('verify', true)
}
</script>

<style lang="scss" scoped></style>
