<!-- 文本省略组件 -->

<template>
  <view>
    <view
      id="text-ellipsis"
      :style="{
        overflow: 'hidden',
        display: '-webkit-box',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': contentShow ? 'unset' : props.rows,
        'line-clamp': contentShow ? 'unset' : props.rows,
      }"
    >
      <view v-if="isHtml(content)">
        <view v-html="content"></view>
      </view>
      <view v-else>{{ content }}</view>
    </view>
    <view
      v-if="buttonShow"
      class="text-[var(--primary-color)] text-24rpx text-center my-10rpx"
      @click="toggle"
    >
      {{ contentShow ? '收起' : '展开' }}
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  content: string
  rows?: number //展示的行数
  lineHeight?: number //行高
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  rows: 1,
  lineHeight: 21,
})
const emits = defineEmits(['toggle'])
const instance = getCurrentInstance()
const query = uni.createSelectorQuery().in(instance.proxy)
const buttonShow = ref(false) //按钮是否显示
const contentShow = ref(true) //是否展开
// 检测文本是否是html
const isHtml = (content: string) => {
  return /<[^>]*>/.test(content)
}

const toggle = () => {
  contentShow.value = !contentShow.value
  emits('toggle', contentShow.value)
}
watch(
  () => props.content,
  () => {
    nextTick(() => {
      query
        .select('#text-ellipsis')
        .boundingClientRect((res) => {
          console.log(' res-->', res.height)
          if (!res) return
          let height = res.height
          const line = height / props.lineHeight
          console.log(' line-->', line, props.rows)
          if (line >= props.rows) {
            buttonShow.value = true
          }
          contentShow.value = false
        })
        .exec()
    })
  },
)

defineExpose({
  toggle,
})
</script>

<style lang="scss" scoped></style>
