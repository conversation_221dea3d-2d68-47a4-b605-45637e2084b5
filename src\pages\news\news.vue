<route lang="json5">
{
  style: {
    navigationBarTitleText: '资讯中心',
  },
}
</route>
<template>
  <div class="container">
    <KyScrollViewV2 v-model:current="current" :tabList="tabList" :apiFun="getDataList">
      <template #default="{ data }">
        <view class="px-20rpx pt-10rpx">
          <view
            v-for="item in data"
            class="my-20rpx px-20rpx py-10rpx bg-#fff box-border h-191rpx box-border overflow-hidden rounded-20rpx gap-44rpx"
            @click="handleClick(item)"
          >
            <view class="flex flex-col justify-between h-100% py-5rpx box-border">
              <view class="text-28rpx text-#333 font-bold">{{ item.title }}</view>

              <view class="flex items-center gap80rpx text-22rpx text-#999999">
                <view class="flex items-center gap7rpx">
                  <image
                    :src="getCdnImageUrl('/common/time.png')"
                    mode="widthFix"
                    class="w-22rpx"
                  />
                  <text class="lh-22rpx">{{ dayjs(item.createTime).format('MM-DD') }}</text>
                </view>
                <view class="flex items-center gap7rpx">
                  <image
                    :src="getCdnImageUrl('/common/view.png')"
                    mode="widthFix"
                    class="w-25rpx"
                  />
                  <text class="lh-22rpx">{{ item.viewcount }}人浏览</text>
                </view>
              </view>
            </view>
            <image
              v-if="item.imgurl"
              :src="item.imgurl"
              class="w-150rpx h-150rpx rounded-10rpx flex-shrink-0"
            />
          </view>
        </view>
      </template>
    </KyScrollViewV2>
  </div>
</template>

<script setup>
import KyScrollViewV2 from '@/components/KyScrollViewV2/KyScrollViewV2.vue'
import { useAppStore } from '@/store'
import { getCdnImageUrl } from '@/utils/images'
import { httpGet } from '@/utils/uniHttp'
import dayjs from 'dayjs'

const appStore = useAppStore()
const current = ref(0)
const tabList = ref([])
const tabsValue = ref([])

const getTabList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/cms/portal/article/category/list', {
      columnId: appStore.baseConfig?.viewNewsChannel,
    }),
  )
  res.forEach((item) => {
    tabList.value.push(item.name)
    tabsValue.value.push(item.id)
  })
  console.log(' res-->', res)
}
const getDataList = async (params) => {
  params.categoryId = tabsValue.value[current.value]
  return httpGet('/cms/portal/article/content/page', params)
}

const handleClick = (item) => {
  uni.navigateTo({
    url: `/pages/news/details?id=${item.id}`,
  })
}

watch(
  () => appStore.baseConfig?.viewNewsChannel,
  (val) => {
    if (!val) return
    getTabList()
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped></style>
