<route lang="json5">
{
  style: {
    navigationBarTitleText: '查看考试结果',
  },
}
</route>
<template>
  <view v-if="!loading" class="container">
    <view class="p-30rpx text-30rpx text-#333333">
      <view>
        {{ currentIndex + 1 }}、[{{ QUESTION_TYPE[currentQuestion.type] }}]
        {{ currentQuestion.content }}
      </view>

      <view style="pointer-events: none">
        <!-- 多选题 -->
        <wd-checkbox-group
          v-if="currentQuestion.type === 'choice_multiple'"
          v-model="currentQuestion.userAnswer"
          shape="square"
          class="!bg-transparent px-30rpx py-20rpx"
        >
          <wd-checkbox v-for="item in currentQuestion.optionList" :modelValue="item.name">
            {{ item.name }}、{{ item.content }}
          </wd-checkbox>
        </wd-checkbox-group>
        <!-- 单选题 -->
        <wd-radio-group
          v-else-if="currentQuestion.type === 'choice_single'"
          v-model="currentQuestion.userAnswer"
          class="!bg-transparent px-30rpx py-20rpx"
          shape="dot"
          inline
        >
          <wd-radio v-for="item in currentQuestion.optionList" :value="item.name">
            {{ item.name }}、{{ item.content }}
          </wd-radio>
        </wd-radio-group>
        <!-- 判断题 -->
        <wd-radio-group
          v-else-if="currentQuestion.type === 'judge'"
          v-model="currentQuestion.userAnswer"
          class="!bg-transparent px-30rpx py-20rpx"
          shape="dot"
          inline
        >
          <wd-radio v-for="item in currentQuestion.optionList" :value="item.name">
            {{ item.content }}
          </wd-radio>
        </wd-radio-group>
      </view>

      <view class="mt-30rpx">
        <view>参考答案</view>
        <view class="text-#666666">
          <text>正确答案：</text>
          <text v-if="currentQuestion.type === 'judge'" class="text-[var(--primary-color)]">
            {{ currentQuestion.answer === 'A' ? '正确' : '错误' }}
          </text>
          <text v-else class="text-[var(--primary-color)]">
            {{ currentQuestion.answer }}
          </text>
          <text>，您的答案：</text>
          <text
            v-if="
              currentQuestion.type === 'choice_multiple' || currentQuestion.type === 'choice_single'
            "
            class="text-[var(--primary-color)]"
          >
            {{
              Array.isArray(currentQuestion.userAnswer)
                ? currentQuestion.userAnswer.join(',')
                : currentQuestion.userAnswer
            }}
          </text>
          <text v-else class="text-[var(--primary-color)]">
            {{ currentQuestion.userAnswer === 'A' ? '正确' : '错误' }}
          </text>
        </view>
      </view>
    </view>

    <view class="absolute bottom-0 left-0 right-0 flex justify-between">
      <wd-button class="flex-1 !rounded-0 !h-46px" plain @click="prevQuestion">上一题</wd-button>
      <wd-button class="flex-1 !rounded-0 !h-46px" @click="nextQuestion">下一题</wd-button>
    </view>
  </view>
</template>

<script setup>
import { QUESTION_TYPE } from '@/utils/dataStatus'
import { httpGet } from '@/utils/uniHttp'

const { toast, showLoading, hideLoading, loading } = useLoading()
let courseId = ''
let classType = ''
const questionList = ref([])
const currentIndex = ref(0)
const currentQuestion = computed(() => questionList.value[currentIndex.value] || {})

const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else {
    toast.show('当前已经是第一题')
  }
}
const nextQuestion = () => {
  if (currentIndex.value < questionList.value.length - 1) {
    currentIndex.value++
  } else {
    toast.show('当前已经是最后一题')
  }
}

const getExamResult = async () => {
  showLoading('加载中...')
  const res = await useRequestWrap(
    () => {
      if (classType === 'ejClass') {
        return httpGet('/study/portal/exam/planExamAnswers', {
          planId: courseId,
        })
      } else {
        return httpGet('/study/portal/exam/examAnswers', {
          courseId,
        })
      }
    },
    () => {
      hideLoading()
    },
  )
  res.questionList.forEach((item) => {
    if (item.type === 'choice_multiple') {
      item.userAnswer = item.userAnswer.split(',')
    }
  })
  questionList.value = res.questionList
}

onLoad((options) => {
  courseId = options.courseId
  classType = options.classType
  getExamResult()
})
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox) {
  display: flex;
  align-items: center;
  .wd-checkbox__shape {
    flex-shrink: 0;
  }
  .wd-checkbox__txt {
    white-space: wrap;
  }
}

:deep(.wd-radio-group) {
  display: flex;
  flex-direction: column;
  .wd-radio {
    margin-bottom: 10px;
    text-align: left;
    &:last-child {
      margin-bottom: 0;
    }
    .wd-radio__label {
      margin-left: 10rpx;
      line-height: 35rpx;
    }
  }
}
</style>
