import { useToast } from 'wot-design-uni'
import { useThemeStore } from '@/store/theme'

export const useLoading = () => {
  const themeStore = useThemeStore()
  const toast = useToast()
  const loading = ref(false)
  // 显示 loading
  const showLoading = (message = '加载中...') => {
    loading.value = true
    return toast.loading({
      loadingColor: themeStore.theme['--primary-color'], // 这里统一设置颜色
      msg: message,
    })
  }

  // 隐藏 loading
  const hideLoading = () => {
    loading.value = false
    toast.close()
  }

  return {
    toast,
    loading,
    showLoading,
    hideLoading,
  }
}
