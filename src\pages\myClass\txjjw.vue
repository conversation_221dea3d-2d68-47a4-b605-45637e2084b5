<template>
  <view class="container">
    <ky-scroll-view :apiFun="() => httpGet('/study/portal/plan/myPlanList')">
      <template #default="{ data }">
        <view v-for="item in data" class="bg-#fff m-20rpx p-20rpx rounded-15rpx">
          <view class="text-#333 text-26rpx space-y-10rpx">
            <view class="font-bold text-30rpx">班级编号：{{ item.serialNumber }}</view>
            <view class="">班级名称：{{ item.planName }}</view>
            <view class="flex justify-between items-center">
              <view>
                培训学时：
                <text class="text-#ff6700">{{ item.bxCredit + item.xxCredit }}</text>
                课时
              </view>
              <view>
                培训费用：
                <text class="text-#ff6700">{{ item.totalPrice }}</text>
                元
              </view>
            </view>
          </view>

          <view class="h-1rpx w-100% bg-#EEE my-20rpx"></view>

          <view class="info space-y-10rpx">
            <view class="item">
              <view>
                报名时间：
                <text>{{ item.createTime?.split(' ')[0] }}</text>
              </view>
              <view class="text-#1a5faa" @click="clickRegisterInfo(item)">报名信息</view>
            </view>
            <view class="item">
              <view>
                缴费时间：
                <text>{{ item.createTime?.split(' ')[0] }}</text>
              </view>
            </view>
            <view class="item">
              <view>
                <text>班级申报：</text>
                <!-- status为04时的逻辑 -->
                <template v-if="item.status === '04'">
                  <text v-if="item.creditStatus === '01'">已申报</text>
                  <text v-else>未申报</text>
                </template>
                <!-- status为03时显示未考完 -->
                <text v-else-if="item.status === '03'">未考完</text>
                <!-- 其他情况显示未学完 -->
                <text v-else>未学完</text>
              </view>
            </view>
          </view>

          <view class="flex gap-20rpx mt-20rpx">
            <view
              v-if="item.examStatus !== '01' && item.examStatus !== '02'"
              class="flex-shrink-0 flex items-center justify-center flex-col gap-6rpx"
              @click="toPage('/pages/exam/view', { courseId: item.planId, classType: 'ejClass' })"
            >
              <image src="@/static/course/jx.png" mode="widthFix" class="w-19px"></image>
              <text class="text-22rpx">查看解析</text>
            </view>
            <wd-button class="w-full" size="large" @click="toStudy(item)">去学习</wd-button>
            <wd-button
              v-if="item.status === '03' && item.examStatus !== '02'"
              class="w-full !bg-#43be39"
              size="large"
              @click="toExam(item)"
            >
              {{ item.examStatus === '03' ? '重新考试' : '去考试' }}
            </wd-button>
          </view>
        </view>
      </template>
    </ky-scroll-view>

    <!-- 报名信息 -->
    <ky-dialog
      v-model:visible="registerInfoVisible"
      title="报名信息"
      :showCancelButton="false"
      @confirm="registerInfoVisible = false"
    >
      <view class="text-left">
        <view class="flex">
          <view>
            <text class="tracking-[2em]">姓</text>
            <text>名：</text>
          </view>
          <view>{{ activeItem.realName }}</view>
        </view>
        <view class="my-10rpx flex">
          <view>身份证号：</view>
          <view>{{ activeItem.idCard }}</view>
        </view>
        <view class="flex">
          <view>手机号码：</view>
          <view>{{ activeItem.phone }}</view>
        </view>
      </view>
    </ky-dialog>
  </view>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import KyScrollView from '@/components/KyScrollView.vue'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'

const toast = useToast()

// 报名信息
const registerInfoVisible = ref(false)
const activeItem = ref({})
const clickRegisterInfo = (item) => {
  activeItem.value = item
  registerInfoVisible.value = true
}

// 进入学习
const toStudy = (item) => {
  uni.setStorageSync('tab', 0)
  uni.switchTab({
    url: '/pages/study/study',
    success: () => {
      setTimeout(() => {
        uni.removeStorageSync('tab')
      }, 1000)
    },
  })
}

// 去考试
const toExam = (item) => {
  uni.setStorageSync('tab', 1)
  uni.switchTab({
    url: '/pages/study/study',
    success: () => {
      setTimeout(() => {
        uni.removeStorageSync('tab')
      }, 1000)
    },
  })
}
</script>

<style lang="scss" scoped>
.info {
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 20rpx;
    font-size: 26rpx;
    background-color: #f2f4ff;
    border-radius: 5rpx;
  }
}
</style>
