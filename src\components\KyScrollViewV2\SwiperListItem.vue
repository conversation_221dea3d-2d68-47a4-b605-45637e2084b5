<!-- 在这个文件对每个tab对应的列表进行渲染 -->
<template>
  <!--  :enable-back-to-top="currentIndex===tabIndex" 在微信小程序上可以多加这一句，因为默认是允许点击返回顶部的，但是这个页面有多个scroll-view，会全部返回顶部，所以需要控制是当前index才允许点击返回顶部 -->
  <!-- 如果当前页已经加载过数据或者当前切换到的tab是当前页，才展示当前页数据（懒加载） -->
  <z-paging
    v-if="firstLoaded || isCurrentPage"
    ref="pagingRef"
    v-model="dataList"
    :fixed="false"
    width="100%"
    height="100%"
    @query="queryList"
  >
    <slot :data="dataList"></slot>
  </z-paging>
  <wd-toast />
</template>

<script setup>
defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps(['tabIndex', 'currentIndex', 'apiFun'])

const { showLoading, hideLoading } = useLoading()

// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref([])
// 当前组件是否已经加载过了
const firstLoaded = ref(false)
// 是否滚动到当前页
const isCurrentPage = ref(false)
const pagingRef = ref(null)
watch(
  () => props.currentIndex,
  (newVal) => {
    if (newVal === props.tabIndex) {
      // 懒加载，当滑动到当前的item时，才去加载
      if (!firstLoaded.value) {
        // 这里需要延迟渲染z-paging的原因是为了避免在一些平台上立即渲染可能引发的底层报错问题
        nextTick(() => {
          setTimeout(() => {
            isCurrentPage.value = true
          }, 10)
        })
      }
    }
  },
  {
    immediate: true,
  },
)

// 接收父组件传过来的刷新列表要求
function reload(type = false) {
  let _type

  if (firstLoaded.value) {
    // 已加载过了
    if (type) {
      _type = true
    } else {
      firstLoaded.value = false
      _type = false
    }
  } else {
    // 未加载过
    if (type) {
      return
    } else {
      _type = false
    }
  }
  // 刷新列表数据(如果不希望列表pageNo被重置可以用refresh代替reload方法)
  setTimeout(() => {
    pagingRef.value.reload(_type)
  }, 100)
}
async function queryList(pageNo, pageSize) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据，请替换成自己的网络请求
  if (!firstLoaded.value) showLoading()
  const res = await useRequestWrap(
    () =>
      props.apiFun({
        current: pageNo,
        size: pageSize,
      }),
    (res) => {
      hideLoading()
    },
  )
  pagingRef.value?.complete(res.records)
  firstLoaded.value = true
}

defineExpose({
  reload,
  refresh: () => {
    pagingRef.value.refresh()
  },
})
</script>

<style lang="scss" scoped>
:deep(.zp-l-text-rpx) {
  font-size: 24rpx;
}
</style>
