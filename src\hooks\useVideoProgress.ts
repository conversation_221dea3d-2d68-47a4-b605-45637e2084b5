import { ref } from 'vue'
import { httpPost } from '@/utils/uniHttp'
interface Options {
  time: number
  params: () => {
    courseId: string
    coursewareId: string
    lessonLocation?: number
    lessonlocation?: number
  }
  handle?: (res: any) => void
}

export const useVideoProgress = (submitOptions: Options, checkPhotoOptions: Options) => {
  const submitTimer = ref<number | null>(null)
  const checkPhotoTimer = ref<number | null>(null)

  function run() {
    clear()
    // 提交进度
    submitTimer.value = setInterval(async () => {
      let res = await submit()
      submitOptions?.handle?.(res)
    }, submitOptions.time)
    // 检查拍照
    if (checkPhotoOptions) {
      checkPhotoTimer.value = setInterval(async () => {
        let res = await checkPhoto()
        checkPhotoOptions?.handle(res)
      }, checkPhotoOptions.time)
    }
  }
  // 提交进度
  async function submit() {
    return await httpPost('/study/portal/play/submit', submitOptions.params())
  }
  // 检查是否需要拍照
  async function checkPhoto() {
    return await useRequestWrap(() =>
      httpPost('/study/portal/play/action/get', checkPhotoOptions.params()),
    )
  }
  // 清除定时器
  function clear() {
    clearInterval(submitTimer.value)
    submitTimer.value = null
    clearInterval(checkPhotoTimer.value)
    checkPhotoTimer.value = null
  }

  return {
    submit,
    checkPhoto,
    clear,
    run,
  }
}
