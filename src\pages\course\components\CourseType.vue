<template>
  <view class="w-180rpx">
    <view
      v-for="item in categoryList"
      @click="handleChange(item)"
      class="flex justify-center items-center h-120rpx flex-col text-#333333"
      :class="{ 'bg-white': active === item.id, line: active === item.id }"
    >
      <text class="text-28rpx text-center">{{ item.categoryName }}</text>
      <!-- <text class="text-20rpx">{{ item.children.length }}门</text> -->
    </view>
  </view>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'

defineProps(['category'])
const emits = defineEmits(['update:category', 'change'])

const categoryList = ref([])
const active = ref(0)

const getData = async () => {
  const res = await useRequestWrap(() => httpGet('/study/portal/category/multi/level/list'))
  categoryList.value = res.filter((item) => {
    if (!Array.isArray(item.children)) {
      item.children = []
    }
    item.children.unshift({
      categoryName: '全部',
      id: 'all',
    })
    return item.categoryName !== '二级建造师'
  })
  active.value = categoryList.value[0].id
  emits('update:category', categoryList.value[0])
}

const handleChange = ({ id }) => {
  active.value = id
  emits(
    'update:category',
    categoryList.value.find((item) => item.id === id),
  )
  emits('change', id)
}

getData()
</script>

<style lang="scss" scoped>
.line {
  position: relative;
  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 6rpx;
    height: 40rpx;
    content: '';
    background: var(--primary-color);
    border-radius: 0;
    transform: translateY(-50%);
  }
}
</style>
