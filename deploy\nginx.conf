server {
  listen 80;
  root /pub;
  client_header_buffer_size 100m;

  index index.html;
  autoindex off;
  charset utf-8;
  proxy_headers_hash_max_size 51200;
  client_max_body_size 40m;

  error_page 404 /404.html;

  location / {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    try_files $uri $uri/ @router;
    index index.html;
  }

  location /api {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    rewrite /api/(.*) /$1 break;
    proxy_pass http://sass-gateway.cloud-edu-sass:9999;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location @router {
      rewrite ^.*$ /index.html last;
  }

  location ~* \.(eot|ttf|woff)$ {
      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
      add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
  }

  location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
      expires      30d;
  }

  location ~ .*\.(js|css)?$ {
    expires      12h;
  }

  location = /index.html {
    add_header Cache-Control "no-cache, no-store";
  }
}

