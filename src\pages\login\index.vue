<route lang="json5">
{
  style: {
    navigationBarTitleText: '登录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view>
    <login v-if="show" />
  </view>
</template>

<script setup>
import { login_hint } from "@/config";
import login from "./login.vue";

const show = ref(false)
const siteName = import.meta.env.VITE_SITE_NAME
let isLogin

if (!login_hint.includes(siteName) || isLogin === 'true') {
  show.value = true
} else {
  window.location.href =
    'https://ggfw.rst.hunan.gov.cn/hrss-pw-ui-hunan/#/login?paySwitch=true&hasLogin=false'
}

onLoad(({ login })=>{
  console.log("=>(index.vue:34) login", login);
  isLogin = login
})
</script>

<style lang="scss" scoped></style>
