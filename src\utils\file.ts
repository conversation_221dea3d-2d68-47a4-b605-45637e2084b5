/**
 * base64转File对象
 * @param {string} base64 - base64字符串
 * @param {string} filename - 文件名
 * @param {string} mimeType - 文件类型，默认为image/jpeg
 * @returns {File} 返回File对象
 */
export function base64ToFile(
  base64: string,
  filename: string = 'file',
  mimeType: string = 'image/jpeg',
): File {
  const base64Data: string = base64.replace(/^data:image\/\w+;base64,/, '')
  const binaryData: string = atob(base64Data)
  const bytes: Uint8Array = new Uint8Array(binaryData.length)
  for (let i: number = 0; i < binaryData.length; i++) {
    bytes[i] = binaryData.charCodeAt(i)
  }
  const blob: Blob = new Blob([bytes], { type: mimeType })
  return new File([blob], filename, { type: mimeType })
}

/**
 * base64 转本地临时文件路径
 * @param {string} base64 - base64字符串
 * @returns {Promise<string>} 返回本地临时文件路径
 */
export function base64ToPath(base64: string): Promise<string> {
  return new Promise((resolve: (value: string) => void, reject: (reason: Error) => void) => {
    try {
      // 解析 base64 字符串
      const [, format, bodyData] = /data:image\/(\w+);base64,(.*)/.exec(base64) || []
      if (!format || !bodyData) {
        reject(new Error('无效的 base64 字符串'))
        return
      }

      // 转换为二进制数据
      const arrayBuffer: ArrayBuffer = uni.base64ToArrayBuffer(bodyData)
      // 生成临时文件路径
      const filePath: string = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.${format}`

      // 写入文件
      uni.getFileSystemManager().writeFile({
        filePath,
        data: arrayBuffer,
        encoding: 'binary',
        success: () => resolve(filePath),
        fail: (error: UniApp.GeneralCallbackResult) =>
          reject(new Error(error.errMsg || '文件写入失败')),
      })
    } catch (error) {
      reject(error instanceof Error ? error : new Error('未知错误'))
    }
  })
}

/**
 * 清理临时文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
export function removeTempFile(filePath: string): Promise<void> {
  return new Promise((resolve: () => void, reject: (reason: unknown) => void) => {
    uni.getFileSystemManager().unlink({
      filePath,
      success: () => resolve(),
      fail: reject,
    })
  })
}
