<template>
  <view class="w-full h-full relative">
    <image
      :src="getCdnImageUrl('/course/rline.png')"
      mode="widthFix"
      class="!absolute bottom-0 left-50% -translate-x-50% z-100 w-110%"
    />
    <video
      class="w-full h-full"
      id="camera-video"
      autoplay
      :controls="false"
      :show-center-play-btn="false"
      disablePictureInPicture
      playsinline
      webkit-playsinline
      x5-playsinline
      x5-video-player-type="h5"
      x-webkit-airplay="deny"
      x5-video-orientation="portraint"
      style="-webkit-transform: scaleX(-1); transform: scaleX(-1)"
    ></video>
  </view>
</template>

<script setup>
import { getCdnImageUrl } from '@/utils/images'
import RecordRTC from 'recordrtc'

const stream = ref(null)
const videoElement = ref(null)
const isInitialized = ref(false)

const init = async () => {
  await initCamera()
  await startPreview()
}

onMounted(() => {
  setTimeout(() => {
    init()
  }, 1000)
})

onUnmounted(() => {
  release()
})

const checkInitialized = () => {
  if (!isInitialized.value) {
    throw new Error('相机未初始化')
  }
}

// 设置视频元素的属性
const setVideoAttributes = () => {
  if (!videoElement.value) return

  // 禁用画中画和其他浏览器控制
  videoElement.value.setAttribute('disablePictureInPicture', 'true')
  videoElement.value.setAttribute('playsinline', 'true')
  videoElement.value.setAttribute('webkit-playsinline', 'true')
  videoElement.value.setAttribute('x5-playsinline', 'true')
  videoElement.value.setAttribute('x5-video-player-type', 'h5')
  videoElement.value.setAttribute('x-webkit-airplay', 'deny')
  videoElement.value.setAttribute('x5-video-orientation', 'portraint')

  // 设置镜像
  videoElement.value.style.transform = 'scaleX(-1)'
  videoElement.value.style.webkitTransform = 'scaleX(-1)'

  // 禁用右键菜单
  videoElement.value.oncontextmenu = () => false

  // 禁用双击全屏
  videoElement.value.ondblclick = (e) => {
    e.preventDefault()
    return false
  }
}

const release = () => {
  // 停止所有轨道
  stream.value?.getTracks().forEach((track) => track.stop())

  // 停止录制
  if (isRecording.value && recorder.value) {
    recorder.value.stop()
  }

  // 清理视频元素
  if (videoElement.value) {
    videoElement.value.srcObject = null
    videoElement.value = null
  }

  // 重置状态
  stream.value = null
  recorder.value = null
  isRecording.value = false
  isInitialized.value = false
}

// 初始化摄像头
const initCamera = async () => {
  release()
  try {
    stream.value = await navigator.mediaDevices.getUserMedia({
      video: true,
      audio: false,
    })
    isInitialized.value = true
  } catch (error) {
    console.log('error -->', error)
    throw new Error('获取摄像头权限失败')
  }
}
// 开始预览
const startPreview = async () => {
  checkInitialized()
  try {
    // 获取video元素
    videoElement.value = document?.querySelector('#camera-video video')
    setVideoAttributes()
    if (!videoElement.value) {
      throw new Error('未找到视频预览元素')
    }
    videoElement.value.srcObject = stream.value
    // 旧的浏览器可能没有srcObject
    if ('srcObject' in videoElement.value) {
      videoElement.value.srcObject = stream.value
    } else {
      videoElement.value.src = window.URL.createObjectURL(stream.value)
    }
    videoElement.value.onloadedmetadata = () => {
      // setVideoAttributes()
      videoElement.value.play()
    }
  } catch (error) {
    console.log('error -->', error)
    throw new Error('启动预览失败')
  }
}

const compressImage = (base64, quality = 0.7, maxWidth = 800) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      // 计算压缩后的尺寸，保持宽高比
      let width = img.width
      let height = img.height
      if (width > maxWidth) {
        height = (maxWidth * height) / width
        width = maxWidth
      }

      canvas.width = width
      canvas.height = height
      ctx.drawImage(img, 0, 0, width, height)

      // 使用指定的质量进行压缩
      const compressedBase64 = canvas.toDataURL('image/jpeg', quality)
      resolve(compressedBase64)
    }
  })
}

const takePhoto = async () => {
  checkInitialized()
  try {
    const canvas = document.createElement('canvas')
    if (!videoElement.value) {
      throw new Error('视频元素未初始化')
    }
    const videoWidth = videoElement.value.videoWidth
    const videoHeight = videoElement.value.videoHeight

    canvas.width = videoWidth
    canvas.height = videoHeight

    const ctx = canvas.getContext('2d')
    ctx.drawImage(videoElement.value, 0, 0, videoWidth, videoHeight)

    const originalBase64 = canvas.toDataURL('image/jpeg')
    // 压缩图片
    const compressedBase64 = await compressImage(originalBase64, 0.2, 600)

    // 计算原始图片大小
    const originalSize = (
      ((originalBase64.length - (originalBase64.indexOf(',') + 1)) * 3) /
      4 /
      (1024 * 1024)
    ).toFixed(2)
    console.log('原始照片大小：', originalSize + 'MB')

    // 计算压缩后的图片大小
    const compressedSize = (
      ((compressedBase64.length - (compressedBase64.indexOf(',') + 1)) * 3) /
      4 /
      (1024 * 1024)
    ).toFixed(2)
    console.log('压缩后照片大小：', compressedSize + 'MB')

    return compressedBase64
  } catch (error) {
    throw new Error('拍照失败')
  }
}

// 开始录制视频
const isRecording = ref(false)
const mediaRecorder = ref(null)
const recorder = ref(null)
const startRecord = async () => {
  checkInitialized()
  if (isRecording.value) {
    throw new Error('已在录制中')
  }

  console.log('开始录制')
  isRecording.value = true

  const options = {
    type: 'video',
    mimeType: 'video/mp4', // MP4 格式
    frameRate: 30, // 降低帧率以减小文件体积
    bitsPerSecond: 1000000, // 设置比特率为 1Mbps
    videoBitsPerSecond: 1000000, // 视频比特率
    // width: 450,
    // height: 600,
    video: {
      // width: 450,
      // height: 600,
    },
    // MP4 特有的配置
    disableLogs: true, // 禁用日志
    timeSlice: 1000, // 每秒保存一次
    // 视频编码配置
    videoCodec: 'H264', // 使用 H.264 编码
    // 压缩配置
    recorderType: RecordRTC.MediaStreamRecorder, // 使用 MediaStream 记录器
    // 设置合理的视频约束
    videoConstraints: {
      // width: { ideal: 450, max: 450 },
      // height: { ideal: 600, max: 600 },
      frameRate: { ideal: 30, max: 30 },
      bitrate: 1000000,
    },
  }

  recorder.value = new RecordRTC(stream.value, options)
  recorder.value.startRecording()
}
// 停止录制视频
const stopRecord = async () => {
  checkInitialized()
  if (!isRecording.value) {
    throw new Error('未在录制中')
  }

  return new Promise((resolve) => {
    if (recorder.value) {
      recorder.value.stopRecording(() => {
        const blob = recorder.value.getBlob()
        const url = URL.createObjectURL(blob)
        console.log('视频大小-->', blob.size / 1024 / 1024 + 'mb')
        isRecording.value = false

        resolve(url)
      })
    } else {
      isRecording.value = false
      resolve()
    }
  })
}

defineExpose({
  // 拍照
  takePhoto,
  // 开始录制视频
  startRecord,
  // 停止录制视频
  stopRecord,
  // 释放相机资源
  release,
})
</script>

<style lang="scss"></style>
