<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="container">
    <view class="container my">
      <view
        class="flex items-center gap-20rpx px-35rpx"
        :style="{ marginTop: 'calc(var(--status-bar-height) + 30px)' }"
      >
        <ky-image
          v-if="userStore.userInfo.avatar"
          :src="userStore.userInfo.avatar"
          width="100rpx"
          height="100rpx"
          rounded="99px"
          class="shrink-0"
        />
        <view
          v-else
          class="w-100rpx h-100rpx bg-[var(--primary-color)] flex-shrink-0 rounded-99rpx text-center lh-100rpx text-40rpx text-#fff"
        >
          {{ userStore.userInfo.realName?.charAt(0) }}
        </view>
        <view class="w-full flex flex-col justify-center gap-10rpx pt-4rpx">
          <view class="text-36rpx text-#333333 flex items-center justify-between">
            <text class="lh-36rpx">{{ userStore.userInfo.username }}</text>
            <wd-button
              v-if="rechargeButton_isShow.includes(siteName)"
              type="primary"
              size="small"
              custom-class="m0 !bg-transparent !border-1rpx !border-#333 !border-solid !h-40rpx !min-w-120rpx !text-20rpx recharge"
              @click="rechargeDialogVisible = true"
            >
              立即充值
            </wd-button>
          </view>
          <view
            class="flex items-center gap-10rpx text-22rpx text-#999 mt-10rpx"
            @click="toPage('/pages/myInfo/myInfo')"
          >
            <text class="mb-6rpx">个人资料编辑</text>
            <wd-icon name="arrow-right" size="14px" color="#999"></wd-icon>
          </view>
        </view>
      </view>

      <!-- 余额 -->
      <view
        class="mt30px px-20rpx mx-29rpx rounded-[20rpx_20rpx_0_0] flex justify-between items-center h-92rpx"
        :style="{
          background: 'url(' + getCdnImageUrl('/my/qbbj.png') + ') no-repeat center center',
          backgroundSize: '100% 100%',
        }"
      >
        <view class="flex items-center">
          <image :src="getCdnImageUrl('/my/pay.png')" mode="widthFix" class="w-40rpx" />
          <text class="text-24rpx text-#e9c998 ml20rpx self-end">￥</text>
          <text class="text-32rpx text-#e9c998 font-bold translate-y-[3rpx]">
            {{ userStore.balance.availableBalance?.toFixed(2) || '0.00' }}
          </text>
        </view>
        <view
          class="text-24rpx text-#cccccc flex items-center gap-10rpx"
          @click="toPage('/pages/balanceDetail/balanceDetail')"
        >
          <text class="lh-24rpx">余额明细</text>
          <image :src="getCdnImageUrl('/my/r.png')" mode="widthFix" class="w-26rpx" />
        </view>
      </view>
      <!-- 菜单 -->
      <view
        v-for="(item, index) in menuList"
        :key="index"
        class="menu-wrap bg-#fff rounded-20rpx mb-30rpx"
      >
        <view
          v-for="(menuItem, index) in item"
          :key="index"
          class="menu-item pl-40rpx pr-30rpx py-33rpx flex items-center justify-between"
          @click="menuItem.onClick"
        >
          <view class="flex items-center gap-26rpx">
            <!-- #ifdef H5 -->
            <SvgIcon :icon="menuItem.svg" class="text-36rpx -mr-8rpx"></SvgIcon>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <image :src="getCdnImageUrl(menuItem.icon)" mode="widthFix" class="w-32rpx" />
            <!-- #endif -->
            <text class="text-26rpx text-#333">{{ menuItem.title }}</text>
          </view>
          <image :src="getCdnImageUrl('/my/arrow_right.png')" mode="widthFix" class="w-15rpx" />
        </view>
      </view>

      <!-- 退出登录 -->
      <ky-dialog v-model:visible="logoutDialogVisible" title="提示" @confirm="logout">
        <view>确定退出登录吗？</view>
      </ky-dialog>

      <!-- 账户充值 -->
      <ky-dialog
        v-model:visible="rechargeDialogVisible"
        title="账户充值"
        confirm-text="去联系"
        cancel-text="关闭"
        width="600rpx"
        @confirm="recharge"
      >
        <view class="bg-#f7f7f7 rounded-10rpx text-left py-40rpx px-29rpx text-26rpx text-#333333">
          <view>需要充值请直接联系{{ appName }}何老师</view>
          <view class="mt-20rpx">电话：{{ customerTelephone }}</view>
        </view>
        <image
          :src="getCdnImageUrl('/my/recharge.png')"
          mode="widthFix"
          class="w-86rpx absolute right-79rpx top-18rpx"
        />
      </ky-dialog>
    </view>
    <image
      :src="getCdnImageUrl('/my/user_bg_img.png')"
      mode="widthFix"
      class="w-full h-auto fixed top-0 left-0"
    />
    <TabBar />
  </view>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import KyImage from '@/components/KyImage.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import TabBar from '@/components/TabBar/index.vue'
import { useAppStore } from '@/store'
import { useUserStore } from '@/store/user'
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { rechargeButton_isShow } from '@/utils/visibleConfig'
const siteName = import.meta.env.VITE_SITE_NAME
const userStore = useUserStore()
const appStore = useAppStore()
const appName = import.meta.env.VITE_APP_TITLE

const menuList = ref([])
const getMenu = () => {
  const list = [
    [
      {
        title: '购物车',
        icon: '/my/shopping.png',
        svg: 'shopping',
        onClick: () => toPage('/pages/shoppingCart/shoppingCart'),
      },
      {
        title: '我的订单',
        icon: '/my/myorder.png',
        svg: 'order',
        onClick: () => toPage('/pages/myOrder/myOrder'),
      },
    ],
    [
      {
        title: '退出登录',
        icon: '/my/tcb.png',
        svg: 'logout',
        onClick: () => {
          logoutDialogVisible.value = true
        },
      },
    ],
  ]
  // [{ title: '设置', icon: '/my/zhaq.png' }],

  let index = 1

  // 账户安全
  if (siteName !== 'hnndsydw') {
    list.splice(index, 0, [
      {
        title: '账户安全',
        icon: '/my/zhaq.png',
        svg: 'zhaq',
        onClick: () => toPage('/pages/accountSecurity/accountSecurity'),
      },
      // #ifdef APP-PLUS || H5
      {
        title: '实名认证',
        icon: '/my/smrz.png',
        svg: 'smrz',
        onClick: () => toPage('/pages/realInfo/realInfo'),
      },
      // #endif
    ])
    index++
  }
  console.log(' appStore.userMenu -->', appStore.userMenu)
  // 我的班级
  if (appStore.userMenu.some((item) => item.name == '我的班级') && siteName !== 'hnndsydw') {
    list.splice(index, 0, [
      {
        title: '我的班级',
        icon: '/my/class.png',
        svg: 'class',
        onClick: () => toPage('/pages/myClass/myClass'),
      },
    ])
    index++
  }
  // 发票
  if (siteName === 'hnndsydw') {
    list.splice(index, 0, [
      {
        title: '我的发票',
        icon: '/my/znxk.png',
        svg: 'wdkc11',
        onClick: () => toPage('/pages/myInvoice/myInvoice'),
      },
      {
        title: '集体开票',
        icon: '/my/znxk.png',
        svg: 'wdkc12',
        onClick: () => toPage('/pages/collectiveInvoice/collectiveInvoice'),
      },
    ])
    index++
  }
  // 智能选课
  if (appStore.userMenu.some((item) => item.name == '智能选课')) {
    list.splice(index, 0, [
      {
        title: '智能选课',
        icon: '/my/znxk.png',
        svg: 'znxk',
        onClick: () => toPage('/pages/curriculaVariable/curriculaVariable'),
      },
    ])
    index++
  }
  //
  // 服务电话
  if (siteName === 'gzjxjy') {
    list.splice(index, 0, [
      {
        title: '服务电话',
        svg: 'phone',
        onClick: () => {
          uni.makePhoneCall({
            phoneNumber: customerTelephone.value,
            success: () => {
              console.log('拨号成功')
            },
            fail: (err) => {
              console.error('拨号失败', err)
            },
          })
        },
      },
    ])
    index++
  }

  menuList.value = list
}
watch(
  () => userStore.isLogined,
  async (val) => {
    if (val) {
      await appStore.getUserMenu()
      getMenu()
    }
  },
  { immediate: true },
)

// 退出登录
const logoutDialogVisible = ref(false)
const logout = () => {
  uni.clearStorageSync()
  userStore.clear()
  uni.reLaunch({
    url: '/pages/login/index',
  })
}

// 账户充值
const rechargeDialogVisible = ref(false)
const recharge = () => {
  uni.makePhoneCall({
    phoneNumber: customerTelephone.value, // 替换为你需要拨打的电话号码
    success: () => {
      console.log('拨号成功')
    },
    fail: (err) => {
      console.error('拨号失败', err)
    },
  })
}

// 获取客服电话
const { customerTelephone } = useGetCustomerTelephone()

onShow(() => {
  if (userStore.isLogined) {
    userStore.getUserInfo()
  } else {
    uni.setStorageSync('redirectUrl', '/pages/my/my')
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
})
</script>

<style lang="scss" scoped>
.my {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 20rpx;
  margin: 0;

  .menu-wrap {
    .menu-item {
      border-bottom: 1px solid #f0f0f0;
    }
    .menu-item:last-child {
      border-bottom: none;
    }
  }

  .recharge {
    color: #333 !important;
  }
}
</style>
