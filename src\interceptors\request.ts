import qs from 'qs'
import { useUserStore } from '@/store'
import { platform } from '@/utils/platform'

interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

const beforeRequest = (method) => {
  // 处理 query 参数
  if (method.config.params) {
    const queryStr = qs.stringify(method.config.params)
    method.url = method.url.includes('?')
      ? `${method.url}&${queryStr}`
      : `${method.url}?${queryStr}`
  }

  // 设置超时时间
  method.config.timeout = 10000

  // 设置请求头
  method.config.headers = {
    platform,
    'Tenant-Code': import.meta.env.VITE_SITE_CODE,
    ...method.config.headers,
  }

  // 添加token
  const userStore = useUserStore()
  const { accessToken } = userStore.authToken
  if (accessToken) {
    method.config.headers.Authorization = `Bearer ${accessToken}`
  }
}

const responded = {
  onSuccess: async <T>(response: any) => {
    const { data: responseData, statusCode } = response

    // HTTP 状态码处理
    if (!(statusCode >= 200 && statusCode < 300)) {
      throw new Error(responseData.message || '网络请求失败')
    }

    // 类型断言
    const result = responseData as ApiResponse<T>

    // 业务状态码处理
    if (result.code === 0) {
      return result.data
    } else if (result.code === 1) {
      uni.showToast({
        title: result.msg,
        icon: 'none',
      })
    }

    // 特定业务状态码处理
    // switch (result.code) {
    //   case 401:
    //     const userStore = useUserStore()
    //     userStore.logout()
    //     uni.reLaunch({ url: '/pages/login/login' })
    //     break
    //   // 其他业务码处理...
    // }
    throw new Error(result.msg || '操作失败')
  },

  onError: (error: Error) => {
    // 统一错误提示
    uni.showToast({
      title: error.message || '请求失败',
      icon: 'none',
      duration: 2000,
    })
    return Promise.reject(error)
  },
}

export { beforeRequest, responded }
