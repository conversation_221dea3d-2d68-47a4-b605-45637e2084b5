<route lang="json5">
{
  style: {
    navigationBarTitleText: '课程详情',
  },
}
</route>
<template>
  <view class="container courseDetails relative !overflow-auto">
    <view class="relative">
      <!-- <video
        v-if="playInfo"
        id="studyVideo"
        class="w-full h421rpx"
        autoplay
        :src="playInfo.url"
        :initial-time="activeCw.initialTime"
        :show-progress="playInfo.studystatus === '03'"
        :show-fullscreen-btn="false"
        :enable-progress-gesture="false"
        :show-play-btn="false"
        @timeupdate="handleTimeUpdate"
        @pause="handlePause"
        @play="handlePlay"
      ></video>
      <ky-image
        v-else
        :src="courseDetails.cimage"
        mode="widthfix"
        class="w-full h421rpx"
        rounded="0px"
      /> -->
      <ky-video
        ref="kyVideoCom"
        class="w-full"
        :class="platform === 'h5' ? 'video-height' : 'h-230px'"
        :poster="courseDetails.cimage"
        :courseList="courseDetails.courseWareList"
        :photograph="false"
        :topicVerify="topicVerify_show.includes(siteName)"
        @ended="handleEnded"
      />
      <view
        class="!absolute bottom-0 left-0 w-full z-10 translate-y-[92%]"
        :style="{ opacity: opacity }"
      >
        <wd-tabs v-model="tab" @click="handleTabClick">
          <block v-for="item in tabs" :key="item.name">
            <wd-tab :title="item.title" :name="item.name"></wd-tab>
          </block>
        </wd-tabs>
      </view>
    </view>
    <view
      class="px-20rpx pt-30rpx box-border relative flex flex-col"
      :style="{
        height: studyFlag
          ? getViewHeight('421rpx')
          : getViewHeight('421rpx', AddCartRef?.isShow ? '120rpx' : '30rpx'),
      }"
    >
      <scroll-view
        scroll-y
        scroll-with-animation
        :scroll-into-view="scrollIntoView"
        class="h-full box-border scroll-view"
        @scroll="handleScroll"
        @scrolltolower="handleScrollEnd"
      >
        <!-- 简介 -->
        <view class="top-box bg-#fff rounded-20rpx px22rpx py25rpx">
          <view class="font-bold flex justify-between items-start">
            <text>{{ courseDetails.cname }}</text>
            <view
              v-if="courseDetails.classify === '02'"
              class="bg-[var(--primary-color)] text-22rpx text-#fff lh-1em pl-20rpx pt-7rpx pr-20rpx pb-8rpx rounded-[99rpx_0_0_99rpx] mr-[-20rpx] flex-shrink-0 mt-4rpx"
            >
              套餐
            </view>
          </view>
          <view class="text-#666 text-22rpx lh22rpx mt24rpx mb19rpx">
            适用于{{ courseDetails.reportTime }}年
          </view>
          <view class="flex items-center justify-between">
            <view class="flex items-center gap-31rpx">
              <view class="tag ellipsis">
                {{ courseDetails.topCategoryName }}
              </view>
              <view class="tag ellipsis">{{ courseDetails.period }}学时</view>
            </view>
            <view class="text-[var(--primary-color)] text-24rpx">
              <text>￥</text>
              <text class="text-30rpx">{{ courseDetails.price }}</text>
            </view>
          </view>
        </view>

        <!-- 课程介绍 -->
        <view id="introduce" class="translate-y-[-25px]"></view>
        <view class="mt-26rpx">
          <view class="title">课程简介</view>
          <view class="bg-#fff rounded-20rpx mt-20rpx p20rpx pt-5rpx">
            <!-- <view class="mt20rpx text-26rpx text-#666666" v-html="courseDetails.cdesc"></view> -->
            <KyTextEllipsis
              :content="courseDetails.cdesc"
              class="mt20rpx text-26rpx text-#666666"
            ></KyTextEllipsis>
          </view>
        </view>

        <!-- 课程目录 -->
        <template v-if="courseDetails.classify !== '02'">
          <view id="catalogue" class="translate-y-[-25px]"></view>
          <view class="mt-26rpx">
            <view class="title">课程目录</view>
            <view>
              <view
                v-for="(item, index) in courseDetails.courseWareList"
                :key="item.id"
                class="course-ware flex items-center gap-20rpx text-24rpx bg-#fff justify-between"
              >
                <view class="flex flex-col gap-10rpx flex-1">
                  <view class="flex items-start gap-10rpx">
                    <view class="ware-tag flex-shrink-0" data-text="录播"></view>
                    <view class="line-clamp-1">{{ item.name }}</view>
                  </view>
                  <!-- 已购买可学习 -->
                  <view v-if="isPay && studyFlag">
                    <view class="flex items-center justify-between gap-10rpx text-#999 text-20rpx">
                      <text>{{ formatDateTime(item.period) }}</text>
                      <view>
                        <text>学习进度：</text>
                        <text class="text-[var(--primary-color)]">
                          {{ getLearningProgress(item) }}%
                        </text>
                      </view>
                    </view>
                    <wd-progress
                      class="!pb-0 !pt-10rpx"
                      hide-text
                      :percentage="getLearningProgress(item)"
                      :color="themeStore.primaryColor"
                      :duration="0"
                    ></wd-progress>
                  </view>
                </view>
                <view v-if="isPay && studyFlag">
                  <image
                    v-if="item.playFlag"
                    :src="getCdnImageUrl('/course/pause.png')"
                    mode="widthFix"
                    class="w-60rpx h-60rpx"
                    @click="togglePlay(item)"
                  />
                  <image
                    v-else
                    :src="getCdnImageUrl('/course/play.png')"
                    mode="widthFix"
                    class="w-60rpx h-60rpx"
                    @click="togglePlay(item, index)"
                  />
                </view>
                <view v-else class="text-#666">
                  {{ formatDateTime(item.period) }}
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 课程评价 -->
        <template v-if="courseDetails.classify !== '02'">
          <view id="evaluate" class="translate-y-[-35px]"></view>
          <view class="mt-26rpx pb-30rpx">
            <view class="title mb-20rpx">课程评价</view>
            <view v-if="evaluateList.length" class="bg-#fff rounded-20rpx py-4rpx">
              <view
                v-for="item in evaluateList"
                :key="item.id"
                class="flex my-10rpx rounded-20rpx p-20rpx gap-20rpx border-1rpx border-#EEEEEE border-b-solid last:border-b-0"
              >
                <ky-image :src="item.userAvatar || head" class="w-70rpx h-70rpx" rounded="99px" />
                <view class="text-24rpx">
                  <view class="text-#666">{{ item.userName }}</view>
                  <view class="text-#333 my10rpx">{{ item.content }}</view>
                  <view class="text-#999 text-17rpx">{{ item.createTime }}</view>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 涵盖课程 -->
        <template v-if="courseDetails.classify === '02'">
          <view id="courses" class="translate-y-[-25px]"></view>
          <view class="mt-26rpx pb-20rpx">
            <view class="title mb-20rpx">涵盖课程</view>
            <view>
              <view
                v-for="item in courseDetails.subCourseList"
                class="flex mb-20rpx p-20rpx bg-#fff rounded-10rpx gap-20rpx"
                @click="toPage('/pages/course/details', { id: item.courseId })"
              >
                <KyImage :src="item.cimage" class="w-250rpx h-150rpx flex-shrink-0"></KyImage>
                <view class="flex flex-col justify-between flex-1 overflow-hidden">
                  <view class="w-full truncate text-26rpx">{{ item.cname }}</view>
                  <view class="flex justify-between mb-5rpx items-center">
                    <view>
                      <text class="tag2 mr-10rpx">{{ item.topCategoryName }}</text>
                      <text class="tag2">{{ item.period }}学时</text>
                    </view>
                    <view class="text-[var(--primary-color)]">
                      <text class="text-24rpx">￥</text>
                      <text class="text-28rpx font-bold">{{ item.price }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </scroll-view>
    </view>
    <!-- AddCartRef?.isShow -->
    <view v-if="!studyFlag && isShowAddCart" class="h-120rpx">
      <!-- <view class="flex flex-col items-center gap-4rpx">
          <image src="@img/home/<USER>" mode="widthFix" class="w-38rpx" />
          <text class="text-22rpx text-#333">咨询</text>
        </view> -->
      <view
        class="fixed bottom-0rpx left-0 pl-30rpx pr-20rpx bg-#fff flex justify-center items-center bg-#fff w-full box-border h-120rpx"
      >
        <AddCart
          ref="AddCartRef"
          :course="courseDetails"
          custom-class="flex-1 !h46px !text-28rpx"
          @study="handleStudy"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import AddCart from '@/components/AddCart.vue'
import KyImage from '@/components/KyImage.vue'
import KyTextEllipsis from '@/components/KyTextEllipsis.vue'
import { topicVerify_show } from '@/config'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import KyVideo from '@/pages/classLearning/components/KyVideo/KyVideo.vue'
import head from '@/static/course/head.png'
import { useThemeStore } from '@/store/theme'
import { formatDateTime } from '@/utils/formatDateTime'
import { getViewHeight } from '@/utils/getViewHeight'
import { getCdnImageUrl } from '@/utils/images'
import { sleep } from '@/utils/sleep'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'

const platform = process.env.VUE_APP_PLATFORM
const themeStore = useThemeStore()
const { showLoading, hideLoading } = useLoading()
const siteName = import.meta.env.VITE_SITE_NAME
const AddCartRef = ref(null)
const isShowAddCart = ref(true)
watch(
  () => AddCartRef.value?.isShow,
  (val) => {
    if (val === undefined) return
    isShowAddCart.value = val
  },
  { immediate: true },
)

const courseDetails = ref({})
const getCourseDetails = async (id) => {
  const res = await useRequestWrap(() => httpGet(`/study/portal/course/detail/${id}`))
  res.courseWareList =
    res.courseWareList?.map((item) => {
      item.lessonLocation = item.lessonLocation ?? 0
      // 不再在这里计算learningProgress，而是在模板中使用computed属性
      return {
        ...item,
        playFlag: false,
      }
    }) || []
  // res.courseWareList[1].studyStatus = '02'
  courseDetails.value = res
  console.log(courseDetails, 'courseDetails')

  setTimeout(() => {
    getTopBoxHeight()
  }, 100)
}

// 计算学习进度的方法
const getLearningProgress = (item) => {
  if (item.period === null) return 0
  if (item.studyStatus === '03') {
    return 100
  }
  return Math.min(parseInt((item.lessonLocation / item.period) * 100), 100)
}

// 顶部box
const topBoxHeight = ref(0)
const getTopBoxHeight = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.top-box')
    .boundingClientRect((data) => {
      topBoxHeight.value = Math.ceil(data.height)
    })
    .exec()
}

// tab
const tab = ref('introduce')
const tabs = computed(() => {
  if (courseDetails.value.classify === '02') {
    return [
      { title: '课程介绍', name: 'introduce' },
      { title: '涵盖课程', name: 'courses' },
    ]
  } else {
    return [
      { title: '课程介绍', name: 'introduce' },
      { title: '课程目录', name: 'catalogue' },
      { title: '课程评价', name: 'evaluate' },
    ]
  }
})
const opacity = ref(0)
const scrollIntoView = ref('')
const handleTabClick = ({ name }) => {
  scrollIntoView.value = name
  setTimeout(() => {
    scrollIntoView.value = ''
  }, 500)
}
const handleScroll = (e) => {
  const scrollTop = e.detail.scrollTop
  opacity.value = scrollTop / topBoxHeight.value
}
const handleScrollEnd = () => {
  setTimeout(() => {
    opacity.value = 1
  }, 180)
}

// 播放视频
const kyVideoCom = ref(null) //视频组件
// 获取需要学习的课件
const getCw = () => {
  let cwI = 0
  let result = null

  for (let i = cwI; i < courseDetails.value.courseWareList.length; i++) {
    const courseware = courseDetails.value.courseWareList[i]
    if (!result && courseware.studyStatus === '01') result = { cI: i, cwI: i }
    if (courseware.studyStatus === '02') {
      result = { cwI: i }
      break
    }
  }

  if (courseDetails.value.courseWareList.length && result) {
    result.courseware = courseDetails.value.courseWareList[result.cwI]
  } else {
    result = {
      courseware: null,
      cwI: -1,
    }
  }
  console.log(' result -->', result)
  return result
}
const togglePlay = async (item) => {
  const { courseware } = getCw()
  if (topicVerify_show.includes(siteName)) {
    if (item.studyStatus !== '03' && courseware && courseware.id !== item.id) {
      uni.showToast({
        title: '请继续学习' + courseware.name,
        icon: 'none',
      })
      return
    }
  }
  try {
    showLoading()
    courseDetails.value.courseWareList.forEach(
      (item_cw) => item_cw !== item && (item_cw.playFlag = false),
    )
    await kyVideoCom.value.play(item)
    hideLoading()
  } catch (error) {
    hideLoading()
  }
}

const handleEnded = async () => {
  await sleep(1500)
  if (topicVerify_show.includes(siteName)) {
    const { courseware } = getCw()
    console.log(' courseware -->', courseware)
    if (!courseware) return
    if (kyVideoCom.value.cw.id === courseware.id) return
    togglePlay(courseware)
  }
}

// const handleTimeUpdate = throttle((e) => {
//   activeCw.value.lessonLocation = parseInt(e.detail.currentTime)
//   if (activeCw.value.studyStatus !== '03') {
//     activeCw.value.learningProgress = parseInt((e.detail.currentTime / activeCw.value.period) * 100)
//   }

//   // 获取当前播放速率
//   videoContext.value.playbackRate({ rate: 1 })
// }, 3000)
// const isPlaying = ref(false)
// const handlePause = () => {
//   isPlaying.value = false
// }
// const handlePlay = () => {
//   isPlaying.value = true
// }

// 课程评价
const evaluateList = ref([])
const getEvaluateList = async (id) => {
  const { records } = await useRequestWrap(() =>
    httpGet(`/study/portal/course/comment/page?courseId=${id}`),
  )
  evaluateList.value = records
}

// 去学习
const isPay = computed(() => ['04', '05', '06'].includes(courseDetails.value.courseStatus)) //是否购买了课程
const studyFlag = ref(false)
const handleStudy = () => {
  studyFlag.value = true
  nextTick(() => {
    setTimeout(() => {
      scrollIntoView.value = ''
    }, 500)
  })
}

const init = async (id) => {
  try {
    showLoading()
    await Promise.all([getCourseDetails(id), getEvaluateList(id)])
    if (isPay.value && studyFlag.value && import.meta.env.VITE_SITE_NAME === 'hnndsydw') {
      uni.showModal({
        title: '警告',
        showCancel: false,
        content: '不要开启小窗、拖动、倍速学习，否则学时进度记录无效！',
      })
    }
  } catch (error) {
    console.error('初始化课程详情失败:', error)
  } finally {
    hideLoading()
  }
}

onLoad(({ id, studyFlag: _studyFlag }) => {
  console.log(' _studyFlag -->', _studyFlag)
  studyFlag.value = !!_studyFlag
  init(id)
})

onHide(() => {
  if (!topicVerify_show.includes(siteName)) return
  if (!kyVideoCom.value.cw?.id) return
  uni.navigateBack({
    delta: 1,
    success: function () {
      // 返回成功后显示弹框
      uni.showModal({
        title: '警告',
        confirmText: '继续学习',
        content: '系统检测到您在学习中退至后台，已退出学习页面。请勿退出学习页面！',
        success: (res) => {
          if (res.confirm) {
            toPage(`/pages/course/details?id=${courseDetails.value.courseId}&studyFlag=true`)
          }
        },
      })
    },
  })
})

// onUnload(() => {
//   clear()
// })
</script>

<style scoped lang="scss">
.courseDetails {
  .title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333333;

    &::before {
      width: 8rpx;
      height: 35rpx;
      margin-right: 12rpx;
      content: '';
      background: var(--primary-color);
      border-radius: 4rpx;
    }
  }

  .tag {
    max-width: 200rpx;
    padding: 6rpx 16rpx 7rpx 16rpx;
    font-size: 20rpx;
    line-height: 1;
    color: var(--primary-color);
    border: 1rpx solid var(--primary-color);
    border-radius: 99px;
  }

  .tag2 {
    padding: 4rpx;
    font-size: 18rpx;
    color: #888888;
    border: 1rpx solid #cccccc;
    border-radius: 3rpx;
  }

  .author {
    margin-top: 10px;
    border-radius: 10rpx;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.09);
  }

  .course-ware {
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.09);

    .ware-tag {
      &::after {
        display: block;
        padding: 0 8rpx 5rpx 8rpx;
        font-size: 18rpx;
        color: var(--primary-color);
        content: attr(data-text);
        background: #f5f7fa;
        border: 1px solid var(--primary-color);
        border-radius: 5rpx;
      }
    }
  }
}

:deep(.wd-tabs) {
  height: 100rpx;
  background-color: transparent;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.09);
  .wd-tabs__nav {
    height: 100rpx;
    overflow: hidden;
    .wd-tabs__nav-item {
      height: 100rpx;
      font-size: 30rpx;
      color: #666666;
    }
    .is-active {
      color: #333333;
    }
  }
}
.video-height {
  height: calc(100vw * 9 / 16);
}
</style>
