<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
  },
}
</route>
<template>
  <view class="container">
    <web-view :src="src" />
  </view>
</template>

<script setup>
const src = ref('')
onLoad(() => {
  const agree = JSON.parse(uni.getStorageSync('agree'))
  src.value = agree.src
  uni.setNavigationBarTitle({
    title: agree.title,
  })
})
onUnload(() => {
  uni.removeStorageSync('agree')
})
</script>

<style lang="scss" scoped></style>
