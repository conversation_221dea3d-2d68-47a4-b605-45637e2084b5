<template>
  <Ky-dialog
    v-model:visible="dialogVisible"
    class="dialog"
    @closed="handleClosed"
    @opened="handleOpened"
    @confirm="handleConfirm"
  >
    <view class="text-38rpx text-#fb362e font-bold">请输入验证码</view>
    <img :src="base64Img" alt="" class="mt-40rpx mb-10rpx" />
    <wd-input v-model="code"></wd-input>
  </Ky-dialog>
</template>

<script setup>
import KyDialog from '@/components/KyDialog.vue'
import { httpGet } from '@/utils/uniHttp'
import qs from 'qs'
import { computed } from 'vue'

const { toast, showLoading, hideLoading } = useLoading()
const props = defineProps({
  cw: {
    type: Object,
    default: () => {},
  },
  base64Img: {
    type: String,
  },
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:visible', 'verify', 'opened'])

const dialogVisible = computed({
  get: () => {
    if (typeof props.visible === 'boolean') {
      return props.visible
    }
    return false
  },
  set: (value) => emit('update:visible', value),
})

const code = ref('')
const handleOpened = async () => {
  emit('opened')
}
const handleClosed = () => {}

const handleConfirm = async () => {
  // const params = {
  //   userAnswer: result.value,
  //   courseId: props.cw.courseId,
  //   coursewareId: props.cw.resId,
  // }
  const params = {
    userAnswer: code.value,
  }

  const res = await useRequestWrap(() =>
    httpGet(`/study/portal/play/sydw/captcha/check?${qs.stringify(params)}`),
  )
  if (!res) {
    uni.showToast({
      title: '验证码错误！请重新输入',
      icon: 'none',
    })
    handleOpened()
    return
  }

  dialogVisible.value = false
  emit('verify', true)
}
</script>

<style lang="scss" scoped></style>
