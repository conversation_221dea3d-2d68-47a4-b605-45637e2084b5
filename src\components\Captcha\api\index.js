/*
 * @Author: chenshengle <EMAIL>
 * @Date: 2023-09-26 09:59:56
 * @LastEditors: chenshengle <EMAIL>
 * @LastEditTime: 2023-09-26 10:11:32
 * @FilePath: \aigc_C\src\components\verifition\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 此处可直接引用自己项目封装好的 axios 配合后端联调
 */

import { httpPost } from '@/utils/uniHttp'
// import request from "@/api/axios.js"       //调用项目封装的axios

// 获取验证图片  以及token
export function reqGet(data) {
  return httpPost('/admin-svc/captcha/get', data)
}

// 滑动或者点选验证
export function reqCheck(data) {
  return httpPost('/admin-svc/captcha/check', data)
}
