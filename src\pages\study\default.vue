<template>
  <view v-if="userStore.isLogined" class="container">
    <wd-tabs v-model="tab" @change="onChange" custom-class="top-tabs">
      <block v-for="item in tabs" :key="item.name">
        <wd-tab :title="item.name" :name="item.name" />
      </block>
    </wd-tabs>
    <view class="h-full w-full overflow-hidden bg-#f3f5f9">
      <!-- <component :is="tabs[tab].component"></component> -->
      <Course v-if="tab === '课程'" />
      <Exam v-if="tab === '考试'" />
      <Declare v-if="tab === '申报'" />
      <Certificate v-if="tab === '证明'" />
    </view>

    <TabBar />
  </view>
</template>

<script lang="ts" setup>
import Course from '@/pages/study/components/Course.vue'
import Exam from '@/pages/study/components/Exam.vue'
import Declare from '@/pages/study/components/Declare.vue'
import Certificate from '@/pages/study/components/Certificate/index.vue'
import { useUserStore } from '@/store'
import { declare_isHide, exam_isHide } from '@/utils/visibleConfig'
import TabBar from '@/components/TabBar/index.vue'
const siteName = import.meta.env.VITE_SITE_NAME
const userStore = useUserStore()

const tab = ref('课程')
let tabs = computed(() => {
  const arr = [
    { name: '课程', navigationBarTitleText: '我的课程', component: Course },
    !exam_isHide.includes(siteName) && {
      name: '考试',
      navigationBarTitleText: '我的考试',
      component: Exam,
    },
    !declare_isHide.includes(siteName) && {
      name: '申报',
      navigationBarTitleText: '证明申请',
      component: Declare,
    },
    { name: '证明', navigationBarTitleText: '我的证书', component: Certificate },
  ]
  return arr.filter((item) => item)
})

const onChange = ({ name }) => {
  // const navigationBarTitleText = tabs[index].navigationBarTitleText
  // uni.setNavigationBarTitle({
  //   title: navigationBarTitleText,
  // })
}

onShow(() => {
  if (!userStore.isLogined) {
    uni.setStorageSync('redirectUrl', '/pages/study/study')
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
})

onShow(() => {
  const tabValue = uni.getStorageSync('tab')
  if (tabValue !== '') {
    tab.value = tabValue
  }
})
</script>

<style scoped lang="scss">
.container {
  display: grid;
  grid-template-rows: auto 1fr;
}

:deep(.top-tabs) {
  .wd-tabs__nav-item-text {
    font-size: 32rpx;
    color: #666666;
    transform: translateY(-2rpx);
  }
  .is-active {
    .wd-tabs__nav-item-text {
      color: var(--primary-color) !important;
    }
  }
}

:deep(.wd-tabs__nav) {
  margin: 5rpx 0;
  background-color: transparent;
}
</style>
