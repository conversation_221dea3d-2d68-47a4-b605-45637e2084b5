<template>
  <KyDialog
    v-model:visible="visible"
    :title="title"
    custom-class="signUpDialog-content"
    class="signUpDialog"
    width="90vw"
    @closed="handleDialogClosed"
  >
    <scroll-view
      scroll-y
      :scroll-top="scrollTop"
      class="hint py-20rpx px-30rpx box-border text-left bg-#f7f7f7 rounded-10rpx"
      :style="{ height: getViewHeight('400rpx') }"
      @scrolltolower="isRead = true"
    >
      <view class="text-[var(--primary-color)] text-24rpx lh-32rpx">
        请严格按照拍照示例及验证拍照事项拍摄，如发现不规范拍照则直接退回重学，且只有一次机会；若发现作弊行为，则直接退回不得重学。
      </view>
      <!-- 正确拍照示例 -->
      <view class="successExam">
        <view class="sub-title">正确拍照示例：</view>
        <view class="img-wrapper">
          <image :src="getCdnImageUrl('/hint/normal.png')" mode="widthFix" />
          <view>
            <image :src="getCdnImageUrl('/hint/dd.png')" />
            <text>请端正坐姿，正脸面对摄像头</text>
          </view>
        </view>
      </view>
      <!-- 错误拍照示例 -->
      <view class="errorExam">
        <view class="sub-title">错误拍照示例：</view>
        <view class="error-grid">
          <view v-for="item in eArr" :key="item.text" class="error-item">
            <image :src="getCdnImageUrl(item.url)" mode="widthFix" />
            <view>
              <image :src="getCdnImageUrl('/hint/cwxx.png')" />
              <text>{{ item.text }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 学员须知 -->
      <view class="notice">
        <view class="sub-title" style="margin-bottom: 0">拍照注意事项：</view>
        <view class="notice-content">
          <view>1、禁止使用虛拟摄像头；</view>
          <view>2、避免在窗户边、车门边进行拍摄；</view>
          <view>3、避免人物头顶强光或背光拍摄；</view>
          <view>4、尽量采用200万像素以上的摄像头设备拍摄；</view>
          <view>
            5、禁止斜拍、仰拍、俯拍，保持正脸正对着摄像头，保证头像及肩部80%以上在摄像框内，拍照时请勿挡住脸部，也不能挪开学习位置做无关于学习的动作或拍摄与学习无关的人物，禁止戴口罩、墨镜、帽子等遮盖人脸特征的装饰；
          </view>
          <view>
            6、应穿着整洁、得体、符合学习场景的服装，避免穿着过于暴露的衣服（男士不可裸露上半身，女士不可穿吊带），避免同一时间频繁更换衣服或长时间穿同一件衣服；
          </view>
          <view>7、请于限定时间内完成验证，否则将自动签退。</view>
        </view>
      </view>
      <view class="notice">
        <view class="sub-title" style="margin-bottom: 0">温馨提示：</view>
        <view class="notice-content">
          <view>
            一、注册一个专业需参加继续教育120学时其中必修课60学时，选修课60学时。每增加一个专业还应参加所增加专业60学时的继续教育。
          </view>
          <view>
            二、凡首次在本平台学习二级建造师继续教育的学员，均需按标准及要求先行完善个人信息方可报名学习。填写时需核对信息是否正确，若发现信息有误需在班次有效期内申请修改否则需重新申请学习。
          </view>
          <view>
            三、我网提供“一站式服务”，学员需按要求在线报名、缴费、学习，不得代学、快进、调速、挂播等操作。上课时请保证摄像头为开启状态，在您学习过程中除了完成每章节的学习拍摄任务以外，系统还会不定时进行抓拍监督，学员需注意学习环境，不要影响摄像头对人脸的识别，保证摄像头拍摄到人脸完整性。一旦发现非本人学习、学习过程中无抓拍照片、照片不符合要求或出现挂播、拖动等造成照片连拍的，一律视为无效学时退回重学；如发现有作弊行为的一律清除学时，学员自行承担一切后果；如发现人为干预正常学习或动用技术手段对系统造成影响的，将向主管部门举报。请务必以认真负责的态度完成此次班级学习，保证全程专注投入，如因疏忽造成不良后果由您自行承担。
          </view>
          <view>
            四、每门课程学习有效期为3个月，有效期内未完成学习记录将自动清零。在线学习、考核和学时审核(预计15个工作日左右）全部通过后，将对接“广东省建设执业资格专业人员继续教育信息服务平台”。具体以广东省建设执业资格注册服务平台的“继续教育学时查询”公布的学时记录为准，学时查询网址：
            <wd-text
              text="http://jypt.gdcic.net/pubsearch/"
              type="primary"
              decoration="underline"
              @click="handleOpenUrl('http://jypt.gdcic.net/pubsearch/')"
            ></wd-text>
            。
          </view>
        </view>
      </view>
      <view class="notice">
        <view class="sub-title" style="margin-bottom: 0">课程协议：</view>
        <view class="notice-content">
          <view>
            您在使用本平台提供的各项服务之前，请您务必审慎阅读、充分理解本协议各条款内容，如您不同意本服务协议及，可以主动停止使用我们提供的服务；您一旦使用我们提供的服务，即视为您已了解并完全同意本服务协议各项内容，包括我们对服务协议随时所做的任何修改，并成为我们的用户。
          </view>
          <view>
            1、用户使用账户余额、银行卡、微信支付、支付宝等方式完成支付的，用户应妥善保管相关支付账号及支付密码，任何经由用户支付账号发出的操作指令，均视为用户本人的行为，用户应自行对经由其相关支付账号所进行的一切操作行为负责。
          </view>
          <view>2、课程一经播放，则用户均不得申请退课。</view>
          <view>
            3、用户了解并同意，平台由于客观原因（包括但不限于：更新课程内容、更新授课时间）不定期的对课程进行更新而无需经过您的事先同意。
          </view>
        </view>
      </view>
    </scroll-view>
    <template #footer>
      <view class="mt-30rpx">
        <wd-button plain @click="handleClose" class="mr20rpx">关闭</wd-button>
        <wd-button @click="emits('confirm')" :disabled="!isRead">{{ confirmText }}</wd-button>
      </view>
    </template>
  </KyDialog>
</template>

<script setup>
import { getViewHeight } from '@/utils/getViewHeight'
import { getCdnImageUrl } from '@/utils/images'
import KyDialog from './KyDialog.vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  title: {
    type: String,
  },
  confirmText: {
    type: String,
  },
})
const emits = defineEmits(['confirm', 'close'])
const visible = defineModel('visible')
const isRead = ref(false)
const scrollTop = ref(0)

const eArr = [
  { text: '摄像背光', url: '/hint/cw1.png' },
  { text: '颜色异常', url: '/hint/cw2.png' },
  { text: '戴口罩', url: '/hint/cw3.png' },
  { text: '开车学习', url: '/hint/cw4.png' },
  { text: '摄像模糊', url: '/hint/cw5.png' },
  { text: '多人摄像', url: '/hint/cw6.png' },
  { text: '非完整人像', url: '/hint/cw7.png' },
  { text: '睡觉学习', url: '/hint/cw8.png' },
  { text: '未着衣物', url: '/hint/cw9.png' },
  { text: '怼脸拍', url: '/hint/cw10.png' },
  { text: '闭眼学习', url: '/hint/cw11.png' },
  { text: '换衣频繁', url: '/hint/cw12.png' },
]
const handleOpenUrl = (url) => {
  // #ifdef H5
  window.open(url, '_blank')
  // #endif
  // #ifdef APP-PLUS
  plus.runtime.openURL(url)
  // #endif
}
const handleClose = () => {
  emits('close')
  visible.value = false
}
// 在弹窗关闭时调用
const handleDialogClosed = () => {
  scrollTop.value = 100 //解决uniapp重复设置相同的值，scroll-top不生效的问
  isRead.value = false
  nextTick(() => {
    scrollTop.value = 0
  })
}
</script>

<style lang="scss" scoped>
:deep(.signUpDialog) {
  .wd-popup {
    top: 52%;
    width: 90% !important;
    .signUpDialog-content {
      width: 100% !important;
      .wd-message-box__body {
        padding-top: 20rpx;
      }
      // .wd-message-box__actions {
      //   padding: 30rpx;
      // }
    }
  }
}

.hint {
  .sub-title {
    margin: 10rpx 0;
    font-size: 28rpx;
    font-weight: bold;
    color: #000;
  }
  .successExam {
    margin-top: 20rpx;
  }
  .img-wrapper {
    display: flex;
    flex-direction: column;
    gap: 15rpx;
    align-items: center;
    margin-top: 20rpx;
    font-size: 20rpx;
    color: #333333;
    & > image {
      width: 223rpx;
      height: 290rpx;
    }
    & > view {
      display: flex;
      gap: 5rpx;
      align-items: center;
      image {
        flex-shrink: 0;
        width: 21rpx;
        height: 21rpx;
      }
      text {
        line-height: 18rpx;
      }
    }
  }
  .errorExam {
    margin-top: 32rpx;
    .error-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10rpx;
      .error-item {
        & > image {
          width: 100%;
          height: auto;
        }
        view {
          display: flex;
          gap: 5rpx;
          align-items: center;
          justify-content: center;
          image {
            width: 20rpx;
            height: 20rpx;
          }
          text {
            font-size: 20rpx;
            color: #333333;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .notice {
    margin-top: 32rpx;
    .notice-content {
      font-size: 22rpx;
      line-height: 40rpx;
      color: #666666;
    }
  }
}
</style>
