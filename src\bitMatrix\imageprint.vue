<template>
  <div class="container px-20rpx">
    <div class="my-20rpx">
      <p class="text-#333 font-bold text-center">湖南农业大学国家级专业技术人员</p>
      <p class="text-#333 font-bold text-center">继续教育基地</p>
      <p class="text-center">用户编码：{{ username }}</p>
    </div>
    <wd-img
      :src="imageBase64"
      :preview-src="imageBase64"
      enable-preview
      mode="widthFix"
      class="w-full"
    />
  </div>
</template>

<script setup>
import { httpGet } from '@/utils/uniHttp'
const { showLoading, hideLoading } = useLoading()

const imageBase64 = ref('')
const username = ref('')

const getImage = async (id) => {
  showLoading()
  const res = await useRequestWrap(() =>
    httpGet('/study/portal/archive/certificate/imagePrint', { id }),
  )
  imageBase64.value = res.imageBase64
  username.value = res.username
  console.log(' res -->', res)
  hideLoading()
}

onLoad(({ id }) => {
  getImage(id)
})
</script>

<style lang="scss" scoped></style>
