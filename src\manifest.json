{"name": "广东学习网", "appid": "__UNI__D6AABC8", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "VideoPlayer": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"], "minSdkVersion": 30, "targetSdkVersion": 30, "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {}, "sdkConfigs": {}}, "compatible": {"ignoreVersion": true}, "nativePlugins": {"epii-camera": {"__plugin_info__": {"name": "摄像头组件 相机组件 内嵌的区域相机组件 自动聚焦 聚焦 缩放 变焦 闪光灯 - [试用版，仅用于自定义调试基座]", "description": "摄像头组件,相机组件,内嵌的区域相机组件,可实现拍照，录制视频。支持自定义布局,点击聚焦,手势缩放 qq ", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=3583", "android_package_name": "", "ios_bundle_id": "", "isCloud": true, "bought": 0, "pid": "3583", "parameters": {}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx9a975caac0d77d74", "setting": {"urlCheck": false}, "usingComponents": true, "mergeVirtualHostAttributes": true, "scopedSlotsCompiler": "augmented", "lazyCodeLoading": "requiredComponents"}, "mp-alipay": {"usingComponents": true, "styleIsolation": "shared"}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"router": {"base": "", "mode": "history"}}}