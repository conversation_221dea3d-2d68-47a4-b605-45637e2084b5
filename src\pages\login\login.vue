<route lang="json5">
{
  style: {
    navigationBarTitleText: '登录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="container bg-#ffffff" :style="{ paddingTop: 'var(--status-bar-height)' }">
    <view class="h-44px flex items-center gap-10rpx mb-20rpx mt-10rpx ml23rpx">
      <!-- #ifdef H5 -->
      <SvgIcon icon="arrow-right" class="w-30rpx rotate-180" />
      <!-- #endif -->
      <!-- #ifndef H5 -->
      <image
        :src="getCdnImageUrl('/common/arrow_left_primary.png')"
        mode="widthFix"
        class="w-17rpx"
      />
      <!-- #endif -->
      <text class="text-28rpx lh-28rpx text-[var(--primary-color)]" @click="back">返回</text>
    </view>
    <view class="px65rpx">
      <view
        class="font-bold text-45rpx flex items-center gap10rpx justify-center mt-60rpx mb-80rpx text-#090916"
      >
        <image
          v-if="appStore.baseConfig.viewFaviconIco"
          :src="appStore.baseConfig.viewFaviconIco"
          mode="widthFix"
          class="w-57rpx"
        />
        <image v-else :src="getCdnImageUrl('/common/logo.png')" mode="widthFix" class="w-57rpx" />
        <text>欢迎使用{{ appName }}</text>
      </view>
      <view class="form">
        <!-- 账号 -->
        <view>
          <view class="label">
            <image :src="getCdnImageUrl('/login/user.png')" class="w-28rpx h-28rpx" />
            用户名 / 邮箱 / 手机号
          </view>
          <wd-input placeholder="请输入用户名/邮箱/手机号" v-model="loginParams.account" />
        </view>
        <!-- 密码 -->
        <view>
          <view class="label">
            <image :src="getCdnImageUrl('/login/password.png')" class="w-28rpx h-28rpx" />
            密码
          </view>
          <PasswordInput v-model:password="loginParams.password" />
        </view>
      </view>
      <view class="text-#666 mt37rpx text-24rpx justify-between flex items-center">
        <text></text>
        <text @click="toPage('/pages/forgetPassword/forgetPassword')">忘记密码</text>
      </view>

      <wd-button
        custom-class="mt60rpx !text-32rpx tracking-[20rpx] !w-full !h96rpx"
        :disabled="!loginParams.account || !loginParams.password"
        :round="false"
        :style="{
          boxShadow: `0 0 15px 0 ${getThemeColorWithOpacity()}`,
        }"
        @click="login"
      >
        登录
      </wd-button>

      <wd-button
        custom-class="mt42rpx !text-32rpx tracking-[20rpx] !w-full !h96rpx"
        :round="false"
        @click="toPage('/pages/register/register')"
        plain
      >
        注册
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import PasswordInput from '@/components/PasswordInput.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import { useAppStore, useUserStore } from '@/store'
import { getThemeColorWithOpacity } from '@/utils/color'
import { getCdnImageUrl } from '@/utils/images'
import { tabBarList, toPage } from '@/utils/toPage'
import { httpPost } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'
const appName = import.meta.env.VITE_APP_TITLE
const showLogo = import.meta.env.VITE_APP_SHOW_LOGO
const toast = useToast()
const { showLoading, hideLoading } = useLoading()
const appStore = useAppStore()
const isDev = import.meta.env.DEV
const loginParams = ref({
  account: isDev ? '***********' : '',
  password: isDev ? '123456' : '',
})

// 返回到主页
const back = () => {
  uni.switchTab({ url: '/pages/home/<USER>' })
}

// 表单验证
const validateForm = () => {
  if (!loginParams.value.account) {
    toast.error('请输入账号')
    return false
  }
  if (!loginParams.value.password) {
    toast.error('请输入密码')
    return false
  }
  return true
}

const userStore = useUserStore()
let query = {}
const login = async () => {
  if (!validateForm()) return
  showLoading('登陆中...')
  const { accessToken, refreshToken } = await useRequestWrap(
    () => httpPost('/admin-svc/user/login', loginParams.value),
    () => {
      hideLoading()
    },
  )
  // 存储token
  userStore.authToken = {
    accessToken,
    refreshToken,
  }

  userStore.getUserInfo().then(() => {
    // 返回上一级
    const redirectUrl = uni.getStorageSync('redirectUrl')
    console.log('tabBarList -->', redirectUrl, tabBarList)
    if (redirectUrl) {
      if (tabBarList.includes(redirectUrl)) {
        console.log('switchTab')
        uni.switchTab({
          url: redirectUrl,
          success: () => {
            uni.removeStorageSync('redirectUrl')
          },
        })
      } else {
        console.log('redirectTo')
        uni.redirectTo({
          url: redirectUrl,
          success: () => {
            uni.removeStorageSync('redirectUrl')
          },
        })
      }
    } else {
      uni.reLaunch({
        url: '/pages/home/<USER>',
      })
    }
  })
}

onLoad((option) => {
  query = option
})
</script>

<style lang="scss" scoped>
.form {
  display: flex;
  flex-direction: column;
  gap: 44rpx;
  .label {
    display: flex;
    gap: 6rpx;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    line-height: 20rpx;
    color: #666666;
  }
  :deep(.wd-input) {
    padding-bottom: 10rpx;
    background: transparent;
    &::after {
      background: #eeeeee;
    }

    .wd-input__icon {
      background: transparent;
    }
    input {
      font-size: 32rpx;
    }
    .uni-input-placeholder {
      font-size: 32rpx !important;
      color: #c7c7c7;
    }
  }

  :deep(.is-not-empty) {
    &::after {
      background: #eeeeee !important;
    }
  }
}
</style>
