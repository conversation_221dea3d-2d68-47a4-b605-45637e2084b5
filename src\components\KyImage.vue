<template>
  <wd-img
    :src="src"
    :height="height"
    :width="width"
    :class="class"
    :mode="mode"
    :radius="rounded"
    :enable-preview="enablePreview"
  >
    <template #error>
      <view class="error-wrap">
        <image :src="getCdnImageUrl('/course/empty.jpg')" mode="aspectFill" class="w-full h-full" />
      </view>
    </template>
  </wd-img>
</template>

<script setup lang="ts">
import { getCdnImageUrl } from '@/utils/images'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

type ImageMode =
  | 'top left'
  | 'top right'
  | 'bottom left'
  | 'bottom right'
  | 'right'
  | 'left'
  | 'center'
  | 'bottom'
  | 'top'
  | 'heightFix'
  | 'widthFix'
  | 'aspectFill'
  | 'aspectFit'
  | 'scaleToFill'

const props = withDefaults(
  defineProps<{
    src: string
    rounded?: string
    height?: string
    width?: string
    class?: string
    mode?: ImageMode
    enablePreview?: boolean
  }>(),
  {
    src: '',
    rounded: '10rpx',
  },
)
</script>

<style scoped lang="scss">
.error-wrap {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #eee;
  border-radius: v-bind('props.rounded') !important;
}
</style>
