<template>
  <view>
    <up-popup :show="visible" mode="center" :round="10" @open="handleOpened" @close="handleClosed">
      <view class="popup">
        <view class="title">
          <text class="text">
            {{ props.type === "1" ? "请规范拍照，否则退回重学" : "视频采集，人工审核" }}
          </text>
        </view>
        <view class="wrapper">
          <view v-if="!photo && !video && visible" class="h-full w-full">
            <!-- #ifdef H5 -->
            <H5Camera ref="cameraCom"></H5Camera>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <MPCamera ref="cameraCom"></MPCamera>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <AppCamera ref="cameraCom" width="600rpx" height="800rpx"></AppCamera>
            <!-- #endif -->
          </view>
          <image v-else-if="photo" :src="photo" mode="widthFix" />
          <video
            v-else
            :src="video"
            mode="widthFix"
            class="w-full h-full"
            disablePictureInPicture
            playsinline
            webkit-playsinline
            x5-playsinline
            x5-video-player-type="h5"
            x-webkit-airplay="deny"
            controlsList="nodownload noplaybackrate"
          />
        </view>
        <view class="footer">
          <up-button
            v-if="!photo && !video"
            @click="handleCancel"
            plain
            shape="circle"
            :color="primaryColor"
            text="关闭"
            style="width: 180rpx; marginRight: 20rpx;flex-shrink: 0"
          />
          <up-button
            v-else
            @click="handleReTakePhoto"
            plain
            shape="circle"
            :color="primaryColor"
            :text="props.type === '1' ? '重拍' : '重录'"
            style="width: 180rpx; marginRight: 20rpx;flex-shrink: 0"
          />
          <view v-if="!photo && !video" style="flex: 1">
            <up-button
              v-if="!isRecording"
              shape="circle"
              :color="primaryColor"
              @click="handleTakePhoto"
              :text="props.type === '1' ? '拍摄' : '开始录制'"
            />
            <up-button v-else shape="circle" disabled :text="`录制中(${recordingTime})`" />
          </view>
          <up-button v-else shape="circle" @click="handleSubmit" :color="primaryColor" text="提交" style="flex:1" />
        </view>
      </view>
      <up-loading-page :loading="loading" bg-color="#00000080" :loadingText="loadingText" :loadingColor="primaryColor"
                       :color="primaryColor"></up-loading-page>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { httpUpload } from "@/utils/uniHttp";
import qs from "qs";
import H5Camera from "../Camera/H5-Camera.vue";
import MPCamera from "../Camera/MP-Camera.vue";
import AppCamera from "../Camera/App-Camera.nvue";
import { useThemeStore } from "@/store";
import { defineEmits, defineModel, defineProps, ref, watch } from "vue";

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: "shared"
  }
});

const loading = ref(false);
const loadingText = ref("");
const { primaryColor } = useThemeStore();
// type: 1:拍照 2:录制视频
const props = defineProps<{
  type: "1" | "2"
  cw: any
}>();
const emit = defineEmits(["verify"]);

const visible = defineModel("visible");
const cameraCom = ref(null);
const photo = ref("");
const video = ref("");
// 拍照 & 录制视频
const isRecording = ref(false);
const recordingTime = ref(10);
let recordTimer = null;
const handleTakePhoto = async () => {

  try {
    if (props.type === "1") {
      photo.value = await cameraCom.value.takePhoto();
    } else {
      isRecording.value = true;
      cameraCom.value.startRecord();
      recordTimer = setInterval(async () => {
        recordingTime.value--;
        if (recordingTime.value < 0) {
          isRecording.value = false;
          clearInterval(recordTimer);
          video.value = await cameraCom.value.stopRecord();
        }
      }, 1000);
    }
  } catch (error) {
    console.log("拍照错误-->", error);
  }
};
// 重拍
const handleReTakePhoto = async () => {
  photo.value = "";
  video.value = "";
  isRecording.value = false;
  recordingTime.value = 10;
};

// 提交 1拍照 3视频
const handleSubmit = async () => {
  loadingText.value = "提交中";
  loading.value = true;
  const params = {
    coursewareId: props.cw.resId,
    courseId: props.cw.courseId,
    lessonLocation: props.cw.lessonLocation,
    classify: props.type === "1" ? 1 : 3
  };
  const res = await httpUpload(
    `/study/portal/play/verify/saveRecordFile?${qs.stringify(params)}`,
    props.type === "1" ? photo.value : video.value
  );
  if (res.code !== 0) {
    emit("verify", false);
    visible.value = false;
    loading.value = false;
    uni.showToast({
      title: res.msg,
      icon: "none"
    });
    return;
  }
  emit("verify", true, res.data);
  visible.value = false;
  loading.value = false;
  if (props.type === "1") {
    uni.showToast({
      title: "验证成功",
      icon: "none"
    });
  } else {
    uni.showToast({
      title: "上传成功",
      icon: "none"
    });
  }
};
const handleCancel = () => {
  visible.value = false;
  emit("verify", false);
};

let timer = null;
const handleOpened = () => {
  timer = setTimeout(() => {
    if (visible.value === true) {
      emit("verify", false);
      uni.showToast({
        title: props.type === "1" ? "未上传照片！" : "未上传视频！",
        icon: "none"
      });
      visible.value = false;
    }
  }, 60000);
};
watch(visible, val => {
  if (!val) {
    handleClosed();
  }
});
const handleClosed = () => {
  photo.value = "";
  video.value = "";
  clearTimeout(timer);
  timer = null;
  clearInterval(recordTimer);
  recordTimer = null;
  isRecording.value = false;
  recordingTime.value = 10;
};
</script>

<style lang="scss" scoped>
:deep(.dialog) {
  .wd-message-box__container {
    width: 90vw !important;
  }

  .wd-message-box__body {
    padding: 40rpx 30rpx;
  }
}

.popup {
  padding: 40rpx;

  .title {
    text-align: center;

    .text {
      font-size: 38rpx;
      font-weight: bold !important;
      color: #fb362e;
      text-align: center;
      margin-bottom: 10rpx;
    }
  }

  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 600rpx;
    height: 800rpx;
    // #ifdef APP-PLUS
    aspect-ratio: 3/4;
    margin: 0 auto;
    // #endif
    overflow: hidden;
    background: #000;
    border-radius: 10rpx;
  }

  .footer {
    display: flex;
    flex-direction: row;
    margin-top: 20rpx;
  }
}
</style>
