<template>
  <view>
    <!-- 视频 -->
    <!--    :controls="false"-->
    <!--    :show-progress="false"-->
    <video
      v-if="videoConfig?.url"
      id="studyVideo"
      autoplay
      class="w-full"
      :class="class"
      :src="videoConfig.url"
      :show-fullscreen-btn="false"
      :enable-progress-gesture="false"
      :initial-time="videoConfig.initTime"
      :show-progress="cw.studyStatus === '03'"
      @pause="handlePause"
      @play="handlePlay"
      @timeupdate="handleTimeUpdate"
      @ended="handleEnded"
    ></video>
    <image v-else :src="poster" mode="scaleToFill" :class="class" />
    <!-- 拍照 -->
    <Photograph
      v-model:visible="photographVisible"
      :cw="cw"
      :type="photographType"
      @verify="handleVerify"
    />
    <!-- 抓拍 -->
    <CandidPhotograph
      v-if="candidPhotographVisible && !photographVisible"
      ref="candidPhotographComp"
      :cw="cw"
      @verify="handleCandidVerify"
    />
  </view>
</template>

<script setup>
import { sleep } from '@/utils/sleep'
import { httpGet, httpPost } from '@/utils/uniHttp'
import { throttle } from 'lodash-es'
import CandidPhotograph from '../Photograph/CandidPhotograph-n.vue'
import Photograph from '../Photograph/Photograph-n.nvue'
import { checkPermission } from '@/utils/permission'
import { useVideoProgress } from '@/hooks/useVideoProgress'
import { useRequestWrap } from '@/hooks/useRequestWrap'
import {
  defineEmits,
  defineExpose,
  defineProps,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from 'vue'

defineOptions({
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  poster: {
    type: String,
    default: '',
  },
  courseList: {
    type: Array,
    default: () => [],
  },
  class: {
    type: String,
    default: '',
  },
  clearPlatInfo: {
    type: Function,
    default: () => {},
  },
})
const emit = defineEmits(['ended'])
const instance = getCurrentInstance()

// 提交进度和检查拍照
const { run, clear, checkPhoto, submit } = useVideoProgress(
  {
    time: 30000,
    params: () => ({
      courseId: cw.value.courseId,
      coursewareId: cw.value.resId,
      lessonLocation: videoConfig.lessonLocation,
    }),
    handle: (res) => {
      console.log('submit：', res)
      if (res.code !== 0) {
        uni.showToast({
          title: res.msg,
          icon: 'none',
        })
        cw.value.playFlag = false
      }
    },
  },
  {
    time: 13000,
    params: () => ({
      courseId: cw.value.courseId,
      coursewareId: cw.value.resId,
      lessonlocation: videoConfig.lessonLocation,
    }),
    handle: (res) => {
      console.log('getAction：', res)
      if (res.action === '2') {
        photographType = '1'
        candidPhotographVisible.value = false
        nextTick(() => {
          setTimeout(() => {
            photographVisible.value = true
          }, 200)
        })
        clear()
      }
    },
  },
)

let photographType = '1' // 1:拍照 2:录制视频
const videoConfig = reactive({
  url: '',
  initTime: 0,
  lessonLocation: 0,
})
const cw = ref({})
const videoContext = ref(null)
const photographVisible = ref(false) //拍照弹窗
// 播放视频
let url = ''
const play = async (_cw) => {
  return new Promise(async (resolve, reject) => {
    // 处理重复点击同一视频的情况
    if (cw.value.id === _cw.id && videoConfig.url) {
      cw.value.playFlag = !cw.value.playFlag
      return resolve()
    }

    videoConfig.url = ''

    // 更新当前课件信息
    cw.value = _cw
    const { courseId, resId: coursewareId, studyStatus, lessonLocation } = cw.value
    // 获取视频配置信息
    const beginConfig = await useRequestWrap(() =>
      httpPost('/study/process/record/begin', {
        courseId,
        coursewareId,
        clientFlag: 'app',
      }),
    )
    const playConfig = await useRequestWrap(
      () =>
        httpGet('/study/portal/play/config', {
          courseId,
          coursewareId,
        }),
      (res) => {
        if (res.statusCode === 500) reject(res)
      },
    )
    // 设置视频上下文和配置
    captureConfig = beginConfig.captureConfig
    videoContext.value = uni.createVideoContext('studyVideo', instance)
    console.log('=>(KyVideo.nvue:178) videoContext', videoContext)
    url = playConfig.url
    videoConfig.initTime = studyStatus === '03' ? 0 : lessonLocation
    videoConfig.lessonLocation = lessonLocation

    // 首次学习需要进行拍照检查、开启抓拍摄像头
    if (studyStatus !== '03') {
      const { action } = await checkPhoto()
      if (action === '2') {
        photographType = '1'
        photographVisible.value = true
        return resolve()
      } else {
        candidPhotographVisible.value = true
      }
    }
    videoConfig.url = url
    cw.value.playFlag = true
    resolve()
  })
}
// 拍照结果
const handleVerify = (res, { id } = {}) => {
  console.log('拍照结果 -->', res, id)
  if (!res) {
    cw.value.playFlag = false
    candidPhotographVisible.value = false
    props.clearPlatInfo()
    return
  }
  videoConfig.url = url
  cw.value.playFlag = true
  nextTick(() => {
    setTimeout(() => {
      candidPhotographVisible.value = true
    }, 200)
  })
  run()
  if (photographType === '2') {
    captureConfig.videoId = id
  }
}

// 抓拍
let captureConfig = null //抓拍配置
const candidPhotographComp = ref(null)
const candidPhotographVisible = ref(false) //抓拍弹窗
const handleCandidPhotograph = throttle(async (time) => {
  if (!cw.value.playFlag) return
  if (!captureConfig) return
  console.log('抓拍配置：', time, captureConfig)
  const { imageLocation, imageId, videoLocation, videoId } = captureConfig
  if (!imageId && time > imageLocation) {
    // 照片抓拍
    candidPhotographComp.value.takePhoto()
  } else if (!videoId && time > videoLocation) {
    // 视频抓拍
    photographType = '2'
    photographVisible.value = true
  }
}, 10000)
// 抓拍结果
const handleCandidVerify = (res, { id } = {}) => {
  if (!res) {
    return
  }
  captureConfig.imageId = id
}

const isPlay = ref(false)
// 暂停
const handlePause = () => {
  isPlay.value = false
  cw.value.playFlag = false
  candidPhotographVisible.value = false
  clear()
}
// 播放
const handlePlay = async () => {
  clear()
  isPlay.value = true
  cw.value.playFlag = true
  console.log('cw.value.studyStatus -->', cw.value.studyStatus)
  if (cw.value.studyStatus === '03') return
  candidPhotographVisible.value = true
  const { action } = await checkPhoto()
  if (action === '2') {
    photographType = '1'
    photographVisible.value = true
  } else {
    run()
  }
}
// 时间更新
const handleTimeUpdate = throttle(async (e) => {
  await sleep(1000)
  if (!videoConfig.url) return
  if (cw.value.studyStatus === '03') return
  if (!cw.value.playFlag) return

  const time = parseInt(e.detail.currentTime)
  videoConfig.lessonLocation = time
  // 更新课件进度, 更新视图
  cw.value.lessonLocation = time
  cw.value.learningProgress = parseInt((time / cw.value.period) * 100)
  // 抓拍进度检测
  handleCandidPhotograph(time)
}, 2000)
// 视频结束
const handleEnded = async () => {
  if (cw.value.studyStatus === '03') {
    emit('ended')
    return
  }
  videoConfig.lessonLocation = cw.value.period
  await submit()
  emit('ended')
}

// 播放暂停
let timer
watch(
  () => cw.value?.playFlag,
  (newVal) => {
    if (newVal) {
      timer = setInterval(() => {
        console.log('播放===123=')
        if (isPlay.value) {
          clearInterval(timer)
          return
        }
        videoContext.value.play()
      }, 500)
    } else {
      videoContext.value.pause()
    }
  },
)
onMounted(async () => {
  // #ifndef APP-PLUS
  const res = await checkPermission(['camera', 'record'])
  console.log('res -->', res)
  if (!res.success) {
    uni.navigateBack()
  }
  // #endif
})
onUnmounted(() => {
  clearInterval(timer)
  clear()
})
defineExpose({
  play,
})
</script>

<style lang="scss" scoped></style>
