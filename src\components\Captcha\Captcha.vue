<template>
  <view
    v-if="visible"
    class="fixed top-0 left-0 right-0 bottom-0 w-full h-full overflow-hidden box-border z-100"
  >
    <!-- 遮罩层 -->
    <view class="absolute top-0 left-0 right-0 bottom-0 bg-#0000004d w-full h-full"></view>
    <!-- 主体 -->
    <view class="relative top-40% -translate-y-50% z-1 bg-#fff my-20rpx mx-40rpx rounded-10rpx">
      <view
        class="flex justify-between items-center border-b-1px border-b-#e4e7eb border-b-solid p-25rpx mb-5rpx"
      >
        <text class="lh-1em">请完成安全验证</text>
        <image src="./images/close.png" mode="widthFix" class="w-32rpx" @click="visible = false" />
      </view>
      <view class="relative p-20rpx">
        <VerifySlide ref="puzzleCaptchaRef" @success="handleSuccess"></VerifySlide>
      </view>
    </view>
  </view>
</template>

<script setup>
import VerifySlide from './VerifySlide.vue'

const emits = defineEmits(['success'])
const visible = defineModel('visible')
const puzzleCaptchaRef = ref(null)
// 请求背景图片和验证图片

const handleSuccess = async (distance) => {
  emits('success', distance)
  visible.value = false
}
</script>

<style lang="scss" scoped></style>
