<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级列表',
  },
}
</route>
<!-- 二建班级列表 -->
<template>
  <view class="container" :class="{ '!overflow-auto': !preSignVisible }">
    <view v-for="item in classList" :key="item.id" class="m-40rpx bg-#fff p-30rpx rounded-10rpx">
      <KyImage :src="item.imgPath" class="w-full h-380rpx" />
      <view class="text-#333333 font-bold text-30rpx mt-6rpx mb-10rpx">{{ item.name }}</view>
      <view class="flex flex-between justify-between text-26rpx text-#666">
        <view>
          <text>培训学时：</text>
          <text class="text-[var(--primary-color)]">{{ item.xxCredit + item.bxCredit }}学时</text>
        </view>
        <view>
          <text>培训费用：</text>
          <text class="text-[var(--primary-color)]">{{ item.totalPrice.toFixed(2) }}元</text>
        </view>
      </view>
      <view class="text-22rpx mt-10rpx text-#999999 line-clamp-2">{{ item.description }}</view>
      <EnrollmentManager
        ref="enrollmentManager"
        :item="item"
        classType="ej"
        @preSignOpen="preSignOpen"
        @preSignClose="preSignClose"
      >
        <wd-button plain class="m0" @click="toPage('/pages/myClass/details?id=' + item.id)">
          班级详情
        </wd-button>
      </EnrollmentManager>
    </view>
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import { httpGet } from '@/utils/uniHttp'
import EnrollmentManager from '@/components/EnrollmentManager.vue'
import { toPage } from '@/utils/toPage'

onShareAppMessage(() => {
  return {
    title: '广东学习网',
    path: '/pages/classList/classList',
  }
})
onShareTimeline(() => {
  return {
    title: '广东学习网',
    path: '/pages/classList/classList',
  }
})

const classList = ref([])
const getClassList = async () => {
  const { records } = await useRequestWrap(() => httpGet('/study/portal/plan/page', { size: 100 }))
  classList.value = records
}
// 预报名(解决键盘开启时，布局紊乱问题)
const preSignVisible = ref(false)
const enrollmentManager = ref(null)
const preSignOpen = () => {
  preSignVisible.value = true
}
const preSignClose = () => {
  preSignVisible.value = false
}
onShow(() => {
  getClassList()
})
</script>

<style lang="scss" scoped></style>
