<template>
  <view :style="{ height: getViewHeight('42px', '10rpx') }">
    <ky-scroll-view :apiFun="getDataList" class="h-100% bg-#f3f5f9">
      <template #default="{ data }">
        <view class="px-20rpx pt-10rpx">
          <view
            v-for="item in data"
            class="my-20rpx px-20rpx py-10rpx bg-#fff box-border h-191rpx box-border overflow-hidden rounded-20rpx gap-44rpx"
            @click="handleClick(item)"
          >
            <view class="flex flex-col justify-between h-100% py-5rpx box-border">
              <view class="text-28rpx text-#333 font-bold">{{ item.title }}</view>

              <view class="flex items-center gap80rpx text-22rpx text-#999999">
                <view class="flex items-center gap7rpx">
                  <image
                    :src="getCdnImageUrl('/common/time.png')"
                    mode="widthFix"
                    class="w-22rpx"
                  />
                  <text class="lh-22rpx">
                    {{ dayjs(item.createTime).format('MM-DD') }}
                  </text>
                </view>
                <view class="flex items-center gap7rpx">
                  <image
                    :src="getCdnImageUrl('/common/view.png')"
                    mode="widthFix"
                    class="w-25rpx"
                  />
                  <text class="lh-22rpx">{{ item.viewcount }}人浏览</text>
                </view>
              </view>
            </view>
            <image
              v-if="item.imgurl"
              :src="item.imgurl"
              class="w-150rpx h-150rpx rounded-10rpx flex-shrink-0"
            />
          </view>
        </view>
      </template>
    </ky-scroll-view>
  </view>
</template>

<script setup>
const props = defineProps({
  tab: {
    type: Number,
    default: 0,
  },
})

const tabsValue = ref(['213', 'e2fbf0a907f3473e97b18c6779972348', '201', '202'])
const getDataList = async (params) => {
  params.categoryId = tabsValue.value[props.tab]
  return httpGet('/cms/portal/article/content/page', params)
}

const handleClick = (item) => {
  uni.navigateTo({
    url: `/pages/news/details?id=${item.id}`,
  })
}
</script>

<style lang="scss" scoped></style>
