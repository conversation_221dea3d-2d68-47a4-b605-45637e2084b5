<template>
  <view>
    <view class="relative">
      <image id="imageBg" :src="image" mode="widthFix" class="w-full" />
      <image
        :src="jigsawImage"
        class="h-full w-102rpx absolute top-0 left-0"
        :style="{ left: ctrlRtX + 'px' }"
      />
      <image
        src="./images/refresh.png"
        mode="widthFix"
        class="w-32rpx absolute top-30rpx right-30rpx"
        @click="getPictrue"
      />
    </view>
    <movable-area class="control" :class="state">
      <view v-if="state == 'ready'" class="tips">向右拖动滑块填充拼图</view>
      <view v-if="state != 'ready'" class="belt" :style="{ width: beltWidth + 'px' }"></view>
      <movable-view
        class="ctrl"
        direction="horizontal"
        :damping="40"
        :x="ctrlX"
        @change="handleCtrlChange"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
        @mousedown="handleTouchStart"
        :disabled="state != 'ready' && state != 'move'"
      >
        <image v-if="state == 'move' || state == 'ready'" class="icon" src="./images/arrow.png" />
        <image v-else-if="state == 'success'" class="icon" src="./images/success.png" />
        <image v-else-if="state == 'fail'" class="icon" src="./images/close.png" />
      </movable-view>
    </movable-area>
  </view>
</template>

<script setup>
import { base64ToPath, removeTempFile } from '@/utils/file'
import { aesEncrypt } from './utils/ase'
import { reqCheck, reqGet } from './api'

const emits = defineEmits(['success'])
const backToken = ref('')
const secretKey = ref('')
let ctrlRtX = ref(0)
const ctrlX = ref(0)
const ctrlRatio = ref(0)
const state = ref('ready')
const size = ref({
  width: 0,
  height: 0,
})
const beltWidth = computed(() => {
  return 38 + 1 + (size.value.width - 38) * ctrlRatio.value
})
const instance = getCurrentInstance()
const query = uni.createSelectorQuery().in(instance.proxy)
const serverImageWidth = 310 //服务端图片宽度

const handleCtrlChange = (e) => {
  ctrlRatio.value = Math.min(e.detail.x / (size.value.width - 38), 1)
  ctrlRtX.value = e.detail.x
}
const handleTouchStart = () => {
  state.value = 'move'
  console.log('start')
}
const handleTouchEnd = async () => {
  // 计算滑块位置
  const x = (ctrlRtX.value / imageBgWidth.value) * serverImageWidth
  const data = {
    captchaType: 'blockPuzzle',
    pointJson: aesEncrypt(JSON.stringify({ x, y: 5.0 }), secretKey.value),
    token: backToken.value,
  }
  const res = await reqCheck(data)
  if (res.repCode == '0000') {
    const captchaVerification = aesEncrypt(
      backToken.value + '---' + JSON.stringify({ x, y: 5.0 }),
      secretKey.value,
    )
    state.value = 'success'
    setTimeout(() => {
      emits('success', { captchaVerification })
    }, 1000)
  } else {
    state.value = 'fail'
    setTimeout(() => {
      reload()
    }, 1000)
  }
}

// 请求背景图片和验证图片
const image = ref('')
const imageBgWidth = ref(0) //图片宽度
const jigsawImage = ref('')
async function getPictrue() {
  let data = {
    captchaType: 'blockPuzzle',
  }
  const res = await reqGet(data)
  backToken.value = res.repData.token
  secretKey.value = res.repData.secretKey
  let url = ''
  let jigsawImageUrl = ''
  // #ifdef MP-WEIXIN
  url = await base64ToPath('data:image/png;base64,' + res.repData.originalImageBase64)
  jigsawImageUrl = await base64ToPath('data:image/png;base64,' + res.repData.jigsawImageBase64)
  // #endif
  // #ifndef MP-WEIXIN
  url = 'data:image/png;base64,' + res.repData.originalImageBase64
  jigsawImageUrl = 'data:image/png;base64,' + res.repData.jigsawImageBase64
  // #endif
  image.value = url
  jigsawImage.value = jigsawImageUrl

  query
    .select('#imageBg')
    .boundingClientRect((data) => {
      imageBgWidth.value = data.width
    })
    .exec()
}

function reload() {
  getPictrue()
  state.value = 'ready'
  ctrlX.value = ctrlRtX.value
  nextTick(() => {
    ctrlX.value = 0
  })
}
reload()
onUnmounted(() => {
  // #ifdef MP-WEIXIN
  removeTempFile(image.value)
  removeTempFile(jigsawImage.value)
  // #endif
})
</script>

<style lang="scss" scoped>
.control {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 40px;
  margin-top: 14px;
  background: #f7f9fa;
  border: 1px solid #e4e7eb;
  border-radius: 2px;

  .tips {
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding-left: 40px;
    font-size: 14px;
    color: #45494c;
  }

  .belt {
    position: absolute;
    top: -1px;
    left: -1px;
    box-sizing: border-box;
    width: calc(50% + 2px);
    height: calc(100% + 2px);
    border: 1px solid rgba(0, 0, 0, 0);
    border-radius: 2px;
  }

  .ctrl {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
    transition: background-color 0.12s;

    .icon {
      width: 46%;
      height: 46%;
      pointer-events: none;
    }
  }

  &.move {
    .belt {
      background: #d1e9fe;
      border-color: #1991fa;
    }
    .ctrl {
      background: #1991fa;
    }
  }
  &.fail {
    .belt {
      background: #fce1e1;
      border-color: #f57a7a;
    }
    .ctrl {
      background: #f57a7a;
    }
  }
  &.success {
    .belt {
      background: #d9f3e8;
      border-color: #28b879;
    }
    .ctrl {
      background: #28b879;
    }
  }
}
</style>
