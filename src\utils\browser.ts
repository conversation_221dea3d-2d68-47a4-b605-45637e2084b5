let ua = ''
// #ifdef H5
ua = navigator.userAgent.toLowerCase()
// #endif

// 是否是微信浏览器
export const isWeixinBrowser = () => {
  return ua.indexOf('micromessenger') !== -1
}
// 是否是企业微信浏览器
export const isWorkWeixinBrowser = () => {
  return ua.indexOf('wxwork') !== -1
}
// 是否是chrome浏览器
export const isChromeBrowser = () => {
  const isChromium = window.chrome
  const winNav = window.navigator
  const vendorName = winNav.vendor
  const isOpera = typeof window.opr !== 'undefined'
  const isIEedge = winNav.userAgent.indexOf('Edg') > -1

  return (
    isChromium !== null &&
    typeof isChromium !== 'undefined' &&
    vendorName === 'Google Inc.' &&
    isOpera === false &&
    isIEedge === false
  )
}
// 是否是安卓
const isAndroid = () => {
  return ua.indexOf('android') !== -1
}
// 是否是ios
const isIOS = () => {
  return ua.indexOf('iphone') !== -1
}
// 是否隐藏导航栏
export const shouldHideNavBar = () => {
  // #ifdef H5
  // 安卓
  if (isAndroid()) {
    if (isWeixinBrowser() || isWorkWeixinBrowser()) {
      if (isTabBarPage()) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  }
  // ios
  if (isIOS()) {
    if (isWeixinBrowser() || isWorkWeixinBrowser()) {
      if (isWorkWeixinBrowser()) {
        return true
      } else {
        if (isTabBarPage()) {
          return true
        } else {
          return false
        }
      }
    } else {
      return false
    }
  }
  // #endif
  return false
}

const effectTabBarList = ['pages/home/<USER>', 'pages/course/course', 'pages/study/study']
export const isTabBarPage = () => {
  // 获取当前页面路径
  const currentPagePath = getCurrentPages().pop()?.route || ''
  return effectTabBarList.includes(currentPagePath)
}
