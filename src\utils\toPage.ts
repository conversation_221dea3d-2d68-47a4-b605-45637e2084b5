export const tabBarList = [
  '/pages/home/<USER>',
  '/pages/course/course',
  '/pages/study/study',
  '/pages/my/my',
]

export const toPage = (url: string, query?: Record<string, any>) => {
  const queryString = query ? buildQueryString(query) : ''

  uni.navigateTo({
    url: queryString ? `${url}?${queryString}` : url,
  })
}

export function buildQueryString(query: Record<string, any> = {}): string {
  return Object.keys(query)
    .map((key) => `${key}=${encodeURIComponent(query[key])}`)
    .join('&')
}
