<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="container bg-#ffffff" :style="{ paddingTop: 'var(--status-bar-height)' }">
    <view class="h-44px flex items-center gap-10rpx mb-20rpx mt-10rpx ml23rpx">
      <!-- #ifdef H5 -->
      <SvgIcon icon="arrow-right" class="w-30rpx rotate-180" />
      <!-- #endif -->
      <!-- #ifndef H5 -->
      <image
        :src="getCdnImageUrl('/common/arrow_left_primary.png')"
        mode="widthFix"
        class="w-17rpx"
      />
      <!-- #endif -->
      <text class="text-28rpx lh-28rpx text-[var(--primary-color)]" @click="back">返回</text>
    </view>

    <view class="px65rpx">
      <view class="text-40rpx !text-#333333 font-bold mb-100rpx">用户注册</view>

      <view class="form">
        <!-- 账号 -->
        <view>
          <view class="label !text-#333333">手机号码</view>
          <wd-input placeholder="请输入手机号码" v-model="formData.phone" />
        </view>
        <!-- 身份证号 -->
        <view>
          <view class="label !text-#333333">身份证号码</view>
          <wd-input placeholder="请输入身份证号码" v-model="formData.idCard" />
        </view>
        <!-- 真实姓名 -->
        <view>
          <view class="label !text-#333333">真实姓名</view>
          <wd-input placeholder="请输入真实姓名" v-model="formData.realName" />
        </view>
        <!-- 设置密码 -->
        <view>
          <view class="label !text-#333333">设置密码</view>
          <PasswordInput v-model:password="formData.password" />
        </view>
        <!-- 短信验证码 -->
        <view>
          <view class="label !text-#333333">短信验证码</view>
          <wd-input placeholder="请输入短信验证码" v-model="formData.captchaCode">
            <template #suffix>
              <wd-button size="small" @click="handleGetCode" :disabled="isSend">
                <text v-if="!isSend">获取验证码</text>
                <text v-else>{{ time }}秒后重新获取</text>
              </wd-button>
            </template>
          </wd-input>
        </view>
      </view>

      <view class="text-22rpx text-[var(--primary-color)] flex items-center gap10rpx mt-32rpx">
        <wd-checkbox v-model="checked" shape="square"></wd-checkbox>
        <text @click="openAgreement">同意{{ appName }}协议</text>
      </view>

      <wd-button
        class="mt60rpx !text-32rpx !w-full !h96rpx"
        :style="{
          boxShadow: `0 0 15px 0 ${getThemeColorWithOpacity()}`,
        }"
        :disabled="
          !formData.phone ||
          !formData.idCard ||
          !formData.realName ||
          !formData.password ||
          !formData.captchaCode ||
          !checked
        "
        :round="false"
        @click="handleRegister"
      >
        确认注册
      </wd-button>
    </view>

    <!-- 验证码 -->
    <Captcha v-model:visible="captchaVisible" @success="handleSuccess" />
  </view>
</template>

<script setup>
import Captcha from '@/components/Captcha/Captcha.vue'
import PasswordInput from '@/components/PasswordInput.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { getThemeColorWithOpacity } from '@/utils/color'
import { getCdnImageUrl } from '@/utils/images'
import { idCardReg, nameReg, phoneReg } from '@/utils/regex'
import { toPage } from '@/utils/toPage'
import { httpGet, httpPost } from '@/utils/uniHttp'
import { useToast } from 'wot-design-uni'

const checked = ref(true)
const captchaVisible = ref(false)
const formData = reactive({
  phone: '',
  idCard: '',
  realName: '',
  password: '',
  captchaCode: '',
})
const appName = import.meta.env.VITE_APP_TITLE

const toast = useToast()
const back = () => {
  uni.navigateBack()
}

const openAgreement = () => {
  uni.setStorageSync(
    'agree',
    JSON.stringify({
      src: import.meta.env.VITE_SERVICE_PROTOCOL,
      title: '平台用户协议',
    }),
  )
  toPage('/pages/agree/agree')
}

// 获取验证码
const handleGetCode = async () => {
  if (isSend.value) return
  if (!phoneReg.test(formData.phone)) return toast.show('请输入合规的手机号')

  const res = await useRequestWrap(() =>
    httpGet('/admin-svc/user/phone/register/check', { mobile: formData.phone }),
  )
  if (res) {
    toast.show('手机号已被注册')
    return
  }
  captchaVisible.value = true
}

let captchaVerification = ''
const isSend = ref(false)
const time = ref(60)
let timer = null
const sendCode = async () => {
  const res = await useRequestWrap(() =>
    httpPost('/admin-svc/user/captcha/check/sendCode?mobile=' + formData.phone, {
      captchaVerification,
    }),
  )
  if (!res) return
  time.value = 60
  isSend.value = true
  timer = setInterval(() => {
    time.value--
    if (time.value <= 0) {
      clearInterval(timer)
      isSend.value = false
    }
  }, 1000)
}

const handleRegister = async () => {
  if (!phoneReg.test(formData.phone)) return toast.show('请输入合规的手机号')
  if (!idCardReg.test(formData.idCard)) return toast.show('请输入合法身份证')
  if (!nameReg.test(formData.realName)) return toast.show('请输入真实姓名')
  if (!formData.password.trim()) return toast.show('请输入密码')
  if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,11}$/.test(formData.password))
    return toast.show('密码必须为6-11位数字和字母组合')
  if (!formData.captchaCode.trim()) return toast.show('请输入验证码')
  const res = await useRequestWrap(() => httpPost('/admin-svc/user/register/phone', formData))
  toast.success('注册成功')
  setTimeout(() => {
    toPage('/pages/login/login')
  }, 1000)
}

const handleSuccess = async (data) => {
  captchaVerification = data.captchaVerification
  sendCode()
}
</script>

<style lang="scss" scoped>
:deep(.form) {
  display: flex;
  flex-direction: column;
  gap: 44rpx;
  .label {
    display: flex;
    gap: 6rpx;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    line-height: 20rpx;
    color: #666666;
  }
  .wd-input {
    padding-bottom: 10rpx;
    background: transparent;
    &::after {
      background: #eeeeee;
    }

    .wd-input__icon {
      background: transparent;
    }
    input {
      font-size: 32rpx;
    }
    .uni-input-placeholder {
      font-size: 32rpx !important;
      color: #c7c7c7;
    }
  }

  .is-not-empty {
    &::after {
      background: #eeeeee !important;
    }
  }
}

:deep(.wd-checkbox__shape) {
  --wot-checkbox-size: 28rpx;
}

:deep(.wd-checkbox__label) {
  display: none;
}
</style>
