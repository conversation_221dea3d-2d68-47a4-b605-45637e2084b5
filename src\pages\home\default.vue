<template>
  <view class="container !overflow-auto pb-20rpx">
    <view class="top-wrap flex items-center gap34rpx">
      <wd-input
        v-model="searchValue"
        use-prefix-slot
        readonly
        custom-class="flex-1 rounded-99px"
        placeholder="搜索你想学的课程"
        @click="toPage('/pages/searchCourse/searchCourse')"
      >
        <template #prefix>
          <view class="flex items-center justify-center">
            <image :src="getCdnImageUrl('/home/<USER>')" mode="" class="w-26rpx h-28rpx" />
          </view>
        </template>
      </wd-input>
      <image
        :src="getCdnImageUrl('/home/<USER>')"
        class="w-42rpx h-35rpx"
        @click="toPage('/pages/shoppingCart/shoppingCart')"
      />
    </view>

    <wd-swiper :list="bannerList" autoplay @click="click_banner" height="26vh"></wd-swiper>

    <view v-if="!notice_hint.includes(siteName)" class="notice-wrap">
      <wd-notice-bar
        ref="notice"
        direction="vertical"
        :text="noticeTextList"
        :delay="3"
        color="#666666"
        background-color="#fff"
        custom-class="font-24rpx flex-1 overflow-hidden"
        @click="handleNoticeClick"
      >
        <template #prefix>
          <image
            :src="getCdnImageUrl('/home/<USER>')"
            class="mr10rpx translate-y-[-2rpx] w-33rpx h-33rpx"
          />
        </template>
      </wd-notice-bar>

      <view class="more-wrap" @click="toPage('/pages/news/news')">
        <text>更多</text>
        <wd-icon name="chevron-right-circle" size="28rpx"></wd-icon>
      </view>
    </view>
    <view v-else class="mt-30rpx"></view>

    <view class="part">
      <view class="title">
        <text>最新</text>
        <text>上架</text>
      </view>

      <view class="list !lg:grid-cols-5">
        <view v-for="item in newCourseList" :key="item.id" class="item" @click="click_course(item)">
          <ky-image
            :src="item.cimage"
            width="100%"
            height="194rpx"
            mode="widthFix"
            rounded="10rpx 10rpx 0 0"
          />
          <view class="content">
            <view class="ellipsis text-26rpx font-bold">{{ item.cname }}</view>
            <view class="mt-18rpx mb-21rpx text-20rpx text-#B6B6B6 ellipsis">
              适用于 {{ item.reportTime }} 等年
            </view>
            <view class="flex items-center gap11rpx">
              <view class="tag">{{ item.topCategoryName }}</view>
              <view class="tag">{{ item.period }} 学时</view>
              <view class="flex-1 text-right text-22rpx text-[var(--primary-color)]">
                <text>￥</text>
                <text class="text-26rpx font-bold">{{ item.price }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="professionalList.length" class="part mt30rpx">
      <view class="title">
        <text>专业提升</text>
        <text>课程</text>
      </view>

      <view class="list-professional">
        <view
          v-for="item in professionalList"
          :key="item.id"
          class="item"
          @click="click_course(item)"
        >
          <ky-image :src="item.cimage" width="100%" height="195rpx" rounded="10rpx 0 0 10rpx" />
          <view class="content">
            <view class="ellipsis text-26rpx font-bold text-#333333">{{ item.cname }}</view>
            <view class="my10rpx text-20rpx text-#B6B6B6 ellipsis w-full">
              适用于 {{ item.reportTime }} 等年
            </view>
            <view class="flex items-center gap11rpx">
              <view class="tag">{{ item.topCategoryName }}</view>
              <view class="tag">{{ item.period }} 学时</view>
            </view>
            <view class="text-right text-22rpx text-[var(--primary-color)] mt-10rpx">
              <text>￥</text>
              <text class="text-26rpx font-bold">{{ item.price }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <TabBar />
  </view>
</template>

<script setup>
import KyImage from '@/components/KyImage.vue'
import TabBar from '@/components/TabBar/index.vue'
import { notice_hint } from '@/config'
import { useAppStore } from '@/store'
import { getCdnImageUrl } from '@/utils/images'
import { toPage } from '@/utils/toPage'
import { httpGet } from '@/utils/uniHttp'
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'

onShareAppMessage(() => {
  return {
    title: '广东学习网',
    path: '/pages/home/<USER>',
  }
})
onShareTimeline(() => {
  return {
    title: '广东学习网',
    path: '/pages/home/<USER>',
  }
})

const appStore = useAppStore()
console.log(' appStore-->', appStore.baseConfig.viewNewsNotice)
const siteName = import.meta.env.VITE_SITE_NAME

// 顶部搜索
const searchValue = ref('')

// 轮播图
const bannerList = ref([])
const getBannerList = async () => {
  const res = await useRequestWrap(() =>
    httpGet('/cms/portal/banner/image/list', { type: 'lbt_app_index' }),
  )
  bannerList.value = res.map((item) => {
    delete item.type // 和组件库冲突
    return {
      ...item,
      value: item.imgUrl,
    }
  })
}
const click_banner = ({ item }) => {
  if (item.urlType !== '4') return
  toPage(`/pages/classList/classList?id=${item.id}`)
}

// 公告
const noticeTextList = ref([])
const noticeDataList = ref([])
const getNoticeList = async () => {
  const { records } = await useRequestWrap(() =>
    httpGet('/cms/portal/article/content/page', {
      categoryId: appStore.baseConfig.viewNewsChannel,
    }),
  )
  console.log(' records-->', records)
  noticeTextList.value = records.map((item) => item.title)
  noticeDataList.value = records
}
const handleNoticeClick = ({ index }) => {
  toPage('/pages/news/details', { id: noticeDataList.value[index].id })
}

// 最新课程
const newCourseList = ref([])
const getCourseList = async () => {
  const params = {
    size: 8,
    current: 1,
    orderType: 13,
  }
  const records = await getDataList(params)
  newCourseList.value = records
}
const click_course = (item) => {
  uni.navigateTo({
    url: `/pages/course/details?id=${item.courseId}`,
  })
}

// 专业提升
const professionalList = ref([])
const getProfessionalList = async () => {
  const params = {
    size: 8,
    current: 1,
    orderType: 13,
    courseType: '1dc86f06023c415b87c02d48b2b3237a',
  }
  const records = await getDataList(params)
  professionalList.value = records
}

const getDataList = async (params) => {
  const { records } = await useRequestWrap(() => httpGet('/study/portal/course/page', params))
  return records
}

onLoad(() => {
  getCourseList()
  getProfessionalList()
  getBannerList()
  getNoticeList()
})
</script>

<style lang="scss">
.top-wrap {
  padding: 20rpx 30rpx;
  background: #fff;

  :deep(.wd-input) {
    .wd-input__body {
      padding-left: 10px;
      background: #f3f5f9;
      border: none;
      border-radius: 99px;

      .wd-icon {
        background: #f3f5f9;
        transform: translateY(-2px);
      }
    }

    &::after {
      display: none;
    }
    input {
      line-height: 34px;
    }
    .uni-input-placeholder {
      font-size: 20rpx !important;
    }
  }
}

:deep(.wd-swiper__track) {
  border-radius: 0 !important;
}

.notice-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20rpx;
  margin: 30rpx 20rpx;
  background: #fff;

  :deep(.wd-notice-bar) {
    width: 88%;
    padding-right: 10rpx;

    .wd-notice-bar__content {
      font-size: 24rpx;
      & > view {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .more-wrap {
    display: flex;
    flex-shrink: 0;
    gap: 10rpx;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: var(--primary-color);
  }
}

.part {
  padding: 0rpx 20rpx;

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: var(--primary-color);

    & > text:last-child {
      color: #333;
    }

    &::before {
      width: 8rpx;
      height: 30rpx;
      margin-right: 12rpx;
      content: '';
      background: var(--primary-color);
      border-radius: 3rpx;
    }
  }

  .list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .item {
      overflow: hidden;
      background-color: #fff;
      border-radius: 10rpx;

      .content {
        padding: 19rpx;
      }
    }
  }

  .list-professional {
    .item {
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 49% 1fr;
      height: 195rpx;
      margin-bottom: 30rpx;
      overflow: hidden;
      background-color: #fff;
      border-radius: 10rpx;

      .content {
        padding: 19rpx;
        overflow: hidden;
      }
    }
  }
}
</style>
